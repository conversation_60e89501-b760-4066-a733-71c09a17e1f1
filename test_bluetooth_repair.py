#!/usr/bin/env python3
"""
Test script for bluetooth_repair.py to verify all fixes work correctly.
"""

import subprocess
import sys
import json
import os

def run_command(cmd, timeout=30):
    """Run a command and return the result."""
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            capture_output=True, 
            text=True, 
            timeout=timeout,
            cwd=os.path.dirname(os.path.abspath(__file__))
        )
        return result.returncode, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "Command timed out"

def test_help():
    """Test that help command works."""
    print("Testing --help command...")
    code, stdout, stderr = run_command("python bluetooth_repair.py --help")
    if code == 0 and "Repair a Bluetooth device" in stdout:
        print("✓ Help command works")
        return True
    else:
        print(f"✗ Help command failed: {code}, {stderr}")
        return False

def test_list():
    """Test that list command works and returns valid JSON."""
    print("Testing --list command...")
    code, stdout, stderr = run_command("python bluetooth_repair.py --list")
    if code == 0:
        try:
            devices = json.loads(stdout)
            if isinstance(devices, list):
                print(f"✓ List command works, found {len(devices)} devices")
                # Check for improved device names (no garbled characters)
                garbled_count = 0
                for device in devices:
                    name = device.get("name", "")
                    if any(ord(c) > 127 for c in name if c not in "éñüäöß"):  # Allow some common accented chars
                        garbled_count += 1
                
                if garbled_count == 0:
                    print("✓ No garbled device names found")
                else:
                    print(f"⚠ Found {garbled_count} devices with potentially garbled names")
                return True
            else:
                print("✗ List command returned invalid JSON structure")
                return False
        except json.JSONDecodeError as e:
            print(f"✗ List command returned invalid JSON: {e}")
            return False
    else:
        print(f"✗ List command failed: {code}, {stderr}")
        return False

def test_repair_nonexistent():
    """Test repair with non-existent device to check error handling."""
    print("Testing repair with non-existent device...")
    code, stdout, stderr = run_command("python bluetooth_repair.py 'NonExistentTestDevice12345' --timeout 5")
    if code != 0 and "Device not found" in stdout and "Available paired devices:" in stdout:
        print("✓ Error handling works correctly")
        return True
    else:
        print(f"✗ Error handling failed: {code}, stdout: {stdout[:200]}")
        return False

def test_syntax_validation():
    """Test that Python syntax is valid."""
    print("Testing Python syntax...")
    code, stdout, stderr = run_command("python -m py_compile bluetooth_repair.py")
    if code == 0:
        print("✓ Python syntax is valid")
        return True
    else:
        print(f"✗ Python syntax error: {stderr}")
        return False

def main():
    """Run all tests."""
    print("Running Bluetooth Repair Application Tests")
    print("=" * 50)
    
    tests = [
        test_syntax_validation,
        test_help,
        test_list,
        test_repair_nonexistent,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
        print()
    
    print("=" * 50)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! The application is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
