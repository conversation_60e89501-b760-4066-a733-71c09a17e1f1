import argparse
import ctypes
import json
import logging
import logging.handlers
import os
import platform
import subprocess
import sys
import tempfile
import threading
import traceback
from textwrap import dedent
from typing import List, Sequence, Optional, Callable

try:
    import tkinter as tk
    from tkinter import ttk, messagebox
except Exception:
    tk = None
    ttk = None
    messagebox = None


def is_windows() -> bool:
    return platform.system() == "Windows"


def is_user_admin() -> bool:
    """Return True if the current process has administrative privileges on Windows."""
    if not is_windows():
        return False
    try:
        return bool(ctypes.windll.shell32.IsUserAnAdmin())
    except Exception:
        return False


def relaunch_as_admin_if_needed(should_require_admin: bool) -> bool:
    """If not running as admin and admin is required, relaunch the current process elevated via ShellExecute 'runas' on Windows.

    Returns True if a relaunch was initiated; the current process should exit.
    """
    if not is_windows() or not should_require_admin or is_user_admin():
        return False

    try:
        from subprocess import list2cmdline
        if getattr(sys, "frozen", False):
            exe = sys.executable
            params = list2cmdline(sys.argv[1:])
        else:
            exe = sys.executable
            script = os.path.abspath(sys.argv[0])
            params = list2cmdline([script] + sys.argv[1:])
        rc = ctypes.windll.shell32.ShellExecuteW(None, "runas", exe, params, None, 1)
        return rc > 32
    except Exception as exc:
        logging.getLogger("bt_repair").error("Failed to relaunch as admin: %s", exc)
        print("Error: Failed to elevate privileges. Run as administrator manually.")
        return False


def _powershell_exe() -> str:
    """Prefer 64-bit Windows PowerShell 5.1. If Python is 32-bit on a 64-bit OS, force Sysnative."""
    if not is_windows():
        raise RuntimeError("This script requires Windows.")
    windir = os.environ.get("WINDIR", r"C:\Windows")
    is_wow64_32proc = (
        os.environ.get("PROCESSOR_ARCHITECTURE", "").lower() == "x86"
        and bool(os.environ.get("PROCESSOR_ARCHITEW6432"))
    )
    sysnative_ps = os.path.join(windir, "Sysnative", "WindowsPowerShell", "v1.0", "powershell.exe")
    system32_ps = os.path.join(windir, "System32", "WindowsPowerShell", "v1.0", "powershell.exe")
    if is_wow64_32proc and os.path.exists(sysnative_ps):
        return sysnative_ps
    if os.path.exists(system32_ps):
        return system32_ps
    return "powershell.exe"


def run_powershell_script(ps_script: str, ps_args: Sequence[str], timeout: Optional[int] = None) -> subprocess.CompletedProcess:
    """Run a PowerShell script provided as text with arguments. Returns CompletedProcess."""
    if not is_windows():
        raise RuntimeError("This script requires Windows.")

    # Validate that param block is at the beginning if present
    if "param(" in ps_script.lower():
        lines = ps_script.strip().split('\n')
        first_non_comment = None
        for line in lines:
            stripped = line.strip()
            if stripped and not stripped.startswith('#') and not stripped.startswith('$script:'):
                first_non_comment = stripped.lower()
                break
        if first_non_comment and not first_non_comment.startswith('param(') and "param(" in first_non_comment:
            raise ValueError("PowerShell param block must be the first non-comment statement")

    with tempfile.NamedTemporaryFile(delete=False, suffix=".ps1", mode="w", encoding="utf-8-sig") as tmp:
        tmp.write(ps_script)
        tmp_path = tmp.name

    logger = logging.getLogger("bt_repair")
    try:
        command: List[str] = [
            _powershell_exe(),
            "-sta",
            "-NoProfile",
            "-NonInteractive",
            "-NoLogo",
            "-ExecutionPolicy", "Bypass",
            "-File", tmp_path,
        ] + list(ps_args)

        logger.debug("Running PowerShell: %s", subprocess.list2cmdline(command) if is_windows() else " ".join(command))
        completed = subprocess.run(
            command,
            capture_output=True,
            text=True,
            encoding="utf-8",
            errors="replace",
            timeout=timeout,
        )
        logger.debug(
            "PS returncode=%s\nSTDOUT=%s\nSTDERR=%s",
            completed.returncode,
            (completed.stdout or "").strip(),
            (completed.stderr or "").strip(),
        )
        return completed
    finally:
        try:
            os.remove(tmp_path)
        except OSError:
            pass


def run_powershell_script_streaming(
    ps_script: str,
    ps_args: Sequence[str],
    on_event: Optional[Callable[[dict], None]] = None,
    timeout: Optional[int] = None,
) -> tuple[subprocess.CompletedProcess, str]:
    """Run a PowerShell script and stream progress events in near real-time."""
    if not is_windows():
        raise RuntimeError("This script requires Windows.")

    with tempfile.NamedTemporaryFile(delete=False, suffix=".ps1", mode="w", encoding="utf-8-sig") as tmp:
        tmp.write(ps_script)
        tmp_path = tmp.name

    logger = logging.getLogger("bt_repair")
    try:
        command: List[str] = [
            _powershell_exe(),
            "-sta",
            "-NoProfile",
            "-NonInteractive",
            "-NoLogo",
            "-ExecutionPolicy", "Bypass",
            "-File", tmp_path,
        ] + list(ps_args)

        logger.debug("(streaming) Running PowerShell: %s",
                     subprocess.list2cmdline(command) if is_windows() else " ".join(command))

        proc = subprocess.Popen(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding="utf-8",
            errors="replace",
            bufsize=1
        )

        stdout_chunks: List[str] = []
        stderr_chunks: List[str] = []

        def _read_stdout():
            assert proc.stdout is not None
            for line in proc.stdout:
                stdout_chunks.append(line)
                s = line.strip().lstrip("\ufeff")
                if s.startswith("##EVT## "):
                    payload = s[len("##EVT## "):]
                    try:
                        evt = json.loads(payload)
                        if on_event:
                            on_event(evt)
                    except Exception:
                        logger.debug("Malformed event payload: %r", payload)

        def _read_stderr():
            assert proc.stderr is not None
            for line in proc.stderr:
                stderr_chunks.append(line)
                if on_event:
                    on_event({"step": "stderr", "message": line.rstrip(), "level": "error"})

        t_out = threading.Thread(target=_read_stdout, daemon=True)
        t_err = threading.Thread(target=_read_stderr, daemon=True)
        t_out.start()
        t_err.start()

        try:
            rc = proc.wait(timeout=timeout)
        except subprocess.TimeoutExpired:
            proc.kill()
            rc = -9

        t_out.join(timeout=1.0)
        t_err.join(timeout=1.0)

        full_stdout = "".join(stdout_chunks)
        full_stderr = "".join(stderr_chunks)
        logger.debug("(streaming) PS rc=%s; stderr=%s", rc, full_stderr.strip())

        completed_obj = subprocess.CompletedProcess(command, rc, full_stdout, full_stderr)
        return completed_obj, full_stdout
    finally:
        try:
            os.remove(tmp_path)
        except OSError:
            pass


# Shared PowerShell functions to avoid duplication
PS_SHARED_FUNCS = dedent(
    r'''
    [Console]::OutputEncoding = [System.Text.Encoding]::UTF8
    $ProgressPreference = 'SilentlyContinue'

    function Emit-Event {
        param([string]$Step, [string]$Message, [string]$Level = 'info')
        try {
            $obj = @{ step = $Step; message = $Message; level = $Level }
            $line = "##EVT## " + ($obj | ConvertTo-Json -Compress)
            if ($script:EMIT_EVENTS_TO_STDERR) {
                [Console]::Error.WriteLine($line); [Console]::Error.Flush()
            } else {
                [Console]::WriteLine($line); [Console]::Out.Flush()
            }
        } catch {}
    }

    # WinRT async bridge WITHOUT requiring AsTask(): handle $null ops and poll IAsyncInfo safely
    function Wait-AsyncResult {
        param([Parameter(Mandatory=$false)]$Operation, [int]$MaxSeconds = 60)

        if (-not $Operation) { return $null }

        # Try AsTask if available (best case)
        try {
            Add-Type -AssemblyName System.Runtime.WindowsRuntime -ErrorAction SilentlyContinue | Out-Null
            $t = [System.WindowsRuntimeSystemExtensions]
            $task = $t::AsTask($Operation)
            if ($task) { $null = $task.Wait([Math]::Max(1, $MaxSeconds) * 1000); return $task.Result }
        } catch {}
        try {
            $t2 = [System.Runtime.InteropServices.WindowsRuntime.WindowsRuntimeSystemExtensions]
            $task2 = $t2::AsTask($Operation)
            if ($task2) { $null = $task2.Wait([Math]::Max(1, $MaxSeconds) * 1000); return $task2.Result }
        } catch {}

        # Fallback: poll Status & use GetResults()
        try { $null = $Operation.Status } catch { return $null }
        $deadline = (Get-Date).AddSeconds([Math]::Max(1, $MaxSeconds))
        while ((Get-Date) -lt $deadline) {
            try {
                $st = $Operation.Status
                $stStr = $st.ToString()
            } catch { return $null }
            if ($st -eq 1 -or $stStr -eq 'Completed') { break }
            if ($st -eq 2 -or $stStr -eq 'Canceled' -or $st -eq 3 -or $stStr -eq 'Error') { break }
            Start-Sleep -Milliseconds 150
        }
        try {
            $st = $Operation.Status
            $stStr = $st.ToString()
        } catch { return $null }
        if ($st -eq 1 -or $stStr -eq 'Completed') {
            try { return $Operation.GetResults() } catch { return $null }
        } else {
            return $null
        }
    }

    function Initialize-WinRT {
        try { Add-Type -AssemblyName System.Runtime.WindowsRuntime | Out-Null } catch {}
        try { $null = [Windows.Devices.Enumeration.DeviceInformation, Windows, ContentType=WindowsRuntime] } catch {}
        try { $null = [Windows.Devices.Enumeration.DeviceInformationKind, Windows, ContentType=WindowsRuntime] } catch {}
        try { $null = [Windows.Devices.Bluetooth.BluetoothDevice, Windows, ContentType=WindowsRuntime] } catch {}
        try { $null = [Windows.Devices.Bluetooth.BluetoothLEDevice, Windows, ContentType=WindowsRuntime] } catch {}
    }

    function Log-Event {
        param([string]$Step, [string]$Message, [string]$Level = 'info')
        Emit-Event -Step $Step -Message $Message -Level $Level
    }

    function Get-AllBluetoothDevices {
        Initialize-WinRT
        $selector = 'System.Devices.Aep.ProtocolId:="{e0cbf06c-cd8b-4647-bb8a-263b43f0f974}"'
        $props = @('System.Devices.Aep.DeviceAddress','System.Devices.Aep.IsPaired','System.Devices.Aep.IsConnected','System.Devices.Aep.SignalStrength')

        # Try full-props query
        try {
            $op = [Windows.Devices.Enumeration.DeviceInformation]::FindAllAsync($selector, $props)
            $res = Wait-AsyncResult -Operation $op -MaxSeconds 60
            if ($res) { return $res }
        } catch {}

        # Fallback minimal query
        try {
            $op = [Windows.Devices.Enumeration.DeviceInformation]::FindAllAsync($selector)
            $list = Wait-AsyncResult -Operation $op -MaxSeconds 60
            if (-not $list) { return @() }
        } catch { return @() }

        $enriched = @()
        foreach ($di in $list) {
            try {
                $op2 = [Windows.Devices.Enumeration.DeviceInformation]::CreateFromIdAsync($di.Id, $props)
                $e = Wait-AsyncResult -Operation $op2 -MaxSeconds 60
                if ($e) { $enriched += $e } else { $enriched += $di }
            } catch { $enriched += $di }
        }
        return $enriched
    }

    function Get-PairedAepDevices {
        Initialize-WinRT
        $selector = 'System.Devices.Aep.ProtocolId:="{e0cbf06c-cd8b-4647-bb8a-263b43f0f974}" AND System.Devices.Aep.IsPaired:=System.StructuredQueryType.Boolean#True'
        $props = @('System.Devices.Aep.DeviceAddress','System.Devices.Aep.IsPaired','System.Devices.Aep.IsConnected','System.Devices.Aep.SignalStrength')

        try {
            $op = [Windows.Devices.Enumeration.DeviceInformation]::FindAllAsync($selector, $props)
            $res = Wait-AsyncResult -Operation $op -MaxSeconds 60
            if ($res) { return $res }
        } catch {}

        try {
            $op = [Windows.Devices.Enumeration.DeviceInformation]::FindAllAsync($selector)
            $list = Wait-AsyncResult -Operation $op -MaxSeconds 60
            if (-not $list) { return @() }
        } catch { return @() }

        $enriched = @()
        foreach ($di in $list) {
            try {
                $op2 = [Windows.Devices.Enumeration.DeviceInformation]::CreateFromIdAsync($di.Id, $props)
                $e = Wait-AsyncResult -Operation $op2 -MaxSeconds 60
                if ($e) { $enriched += $e } else { $enriched += $di }
            } catch { $enriched += $di }
        }
        return $enriched
    }

    function Get-RegistryPairedBluetoothDevices {
        $root = 'HKLM:\SYSTEM\CurrentControlSet\Services\BTHPORT\Parameters\Devices'
        $items = @()
        try { $keys = Get-ChildItem -Path $root -ErrorAction Stop } catch { $keys = @() }
        foreach ($k in $keys) {
            # First, calculate the MAC address from the key name
            $addrHex = $k.PSChildName -replace '[^0-9A-Fa-f]', ''
            if ($addrHex.Length -ge 12) {
                $pairs = ($addrHex.ToUpper() -split '(.{2})' | Where-Object { $_ -ne '' })
                $addr = ($pairs | ForEach-Object { $_ }) -join ':'
            } else { $addr = $null }

            try {
                $props = Get-ItemProperty -Path $k.PSPath -Name 'Name' -ErrorAction Stop
                $bytes = $props.Name
                if ($bytes -is [byte[]]) {
                    try {
                        # Try Unicode first
                        $nm = [System.Text.Encoding]::Unicode.GetString($bytes).Trim([char]0)
                        # Check if the decoded name contains mostly ASCII printable characters
                        $asciiChars = ($nm.ToCharArray() | Where-Object { [int]$_ -ge 32 -and [int]$_ -le 126 }).Count
                        $totalChars = $nm.Length
                        if ($totalChars -eq 0 -or ($asciiChars / $totalChars) -lt 0.5) {
                            # If mostly non-ASCII, fall back to formatted MAC address
                            if ($addr) {
                                $nm = "Device_" + ($addr -replace ':', '')
                            } else {
                                $nm = $k.PSChildName
                            }
                        }
                    } catch {
                        $nm = $k.PSChildName
                    }
                } else {
                    $nm = if ($bytes) { $bytes.ToString() } else { $k.PSChildName }
                }
            } catch { $nm = $k.PSChildName }
            $items += [PSCustomObject]@{ name=$nm; address=$addr; isPaired=$true; isConnected=$null; signal=$null }
        }
        return $items
    }
    '''
)

PS_REPAIR_SCRIPT = (
    dedent(r'''
    param(
        [Parameter(Mandatory=$true)][string]$DeviceName,
        [switch]$RestartService,
        [switch]$ToggleAdapter,
        [int]$TimeoutSeconds = 60
    )
    ''')
    + PS_SHARED_FUNCS
    + dedent(r'''
    function Wait-ForAepDevice {
        param([string]$Address, [string]$Pattern, [int]$TimeoutSeconds = 20)
        $sw = [System.Diagnostics.Stopwatch]::StartNew()
        while ($sw.Elapsed.TotalSeconds -lt $TimeoutSeconds) {
            $devices = Get-AllBluetoothDevices
            if (-not $devices) { $devices = @() }
            $found = @($devices | Where-Object {
                ($Address -and $_.Properties['System.Devices.Aep.DeviceAddress'] -eq $Address) -or
                ($Pattern -and $_.Name -like $Pattern)
            }) | Select-Object -First 1
            if ($found) { return $found }
            Start-Sleep -Milliseconds 800
        }
        return $null
    }

    function Convert-MacToUInt64 {
        param([string]$Mac)
        if (-not $Mac) { return $null }
        $clean = ($Mac -replace '[:\-]', '')
        if ($clean.Length -lt 12) { return $null }
        return [UInt64]::Parse($clean.Substring(0,12), [System.Globalization.NumberStyles]::HexNumber)
    }

    function Pair-ByAddress {
        param([string]$Address)
        $addr64 = Convert-MacToUInt64 -Mac $Address
        if (-not $addr64) { return $null }
        try {
            $bdOp = [Windows.Devices.Bluetooth.BluetoothDevice]::FromBluetoothAddressAsync($addr64)
            $bd = Wait-AsyncResult -Operation $bdOp
            if ($bd) {
                $diOp = [Windows.Devices.Enumeration.DeviceInformation]::CreateFromIdAsync($bd.DeviceId)
                $di = Wait-AsyncResult -Operation $diOp
                if ($di) {
                    if ($di.Pairing.IsPaired) { try { $null = Wait-AsyncResult -Operation ($di.Pairing.UnpairAsync()) } catch {} }
                    $pr = Wait-AsyncResult -Operation ($di.Pairing.PairAsync())
                    if ($pr) { return @{ status = $pr.Status; name = $di.Name; isPaired = $di.Pairing.IsPaired } }
                }
            }
        } catch {}
        try {
            $leOp = [Windows.Devices.Bluetooth.BluetoothLEDevice]::FromBluetoothAddressAsync($addr64)
            $le = Wait-AsyncResult -Operation $leOp
            if ($le) {
                $diOp = [Windows.Devices.Enumeration.DeviceInformation]::CreateFromIdAsync($le.DeviceId)
                $di = Wait-AsyncResult -Operation $diOp
                if ($di) {
                    if ($di.Pairing.IsPaired) { try { $null = Wait-AsyncResult -Operation ($di.Pairing.UnpairAsync()) } catch {} }
                    $pr = Wait-AsyncResult -Operation ($di.Pairing.PairAsync())
                    if ($pr) { return @{ status = $pr.Status; name = $di.Name; isPaired = $di.Pairing.IsPaired } }
                }
            }
        } catch {}
        return $null
    }

    function Remove-BluetoothDeviceByRegistry {
        param([string]$Address, [string]$NamePattern)
        $result = @{ removedRegistry = $false; uninstalledPnP = @() }
        if ($Address) {
            $hex = ($Address -replace '[:\-]', '').ToUpper()
            $regPath = 'HKLM:\SYSTEM\CurrentControlSet\Services\BTHPORT\Parameters\Devices\' + $hex
            try { if (Test-Path $regPath) { Remove-Item -Path $regPath -Recurse -Force -ErrorAction Stop; $result.removedRegistry = $true } } catch {}
        }
        try {
            $pnps = Get-PnpDevice -Class Bluetooth -ErrorAction SilentlyContinue
            foreach ($p in $pnps) {
                $n = if ($p.FriendlyName) { $p.FriendlyName } elseif ($p.Name) { $p.Name } else { '' }
                if ($Address -and $p.InstanceId -match (($Address -replace '[:\-]', '').ToUpper())) {
                    try { Uninstall-PnpDevice -InstanceId $p.InstanceId -Confirm:$false -ErrorAction SilentlyContinue | Out-Null; $result.uninstalledPnP += $p.InstanceId } catch {}
                } elseif ($NamePattern -and $n -like $NamePattern) {
                    try { Uninstall-PnpDevice -InstanceId $p.InstanceId -Confirm:$false -ErrorAction SilentlyContinue | Out-Null; $result.uninstalledPnP += $p.InstanceId } catch {}
                }
            }
        } catch {}
        return $result
    }

    function Restart-BluetoothStackIfRequested {
        param([switch]$RestartService, [switch]$ToggleAdapter)
        $didAnything = $false
        if ($RestartService) {
            try {
                $didAnything = $true
                Emit-Event -Step 'reset' -Message 'Restarting Bluetooth service (bthserv)...'
                Restart-Service -Name 'bthserv' -ErrorAction Stop -Force | Out-Null
            } catch { Emit-Event -Step 'reset' -Message ('Failed to restart bthserv: ' + $_.Exception.Message) -Level 'error' }
            try {
                $deadline = (Get-Date).AddSeconds(20)
                do {
                    $svc = Get-Service -Name 'bthserv' -ErrorAction SilentlyContinue
                    if ($svc -and $svc.Status -eq 'Running') { break }
                    Start-Sleep -Milliseconds 500
                } while ((Get-Date) -lt $deadline)
                $svc = Get-Service -Name 'bthserv' -ErrorAction SilentlyContinue
                if ($svc -and $svc.Status -eq 'Running') { Emit-Event -Step 'reset' -Message 'Bluetooth service is running.' -Level 'success' }
                else { Emit-Event -Step 'reset' -Message 'Bluetooth service did not reach Running state in time.' -Level 'error' }
            } catch {}
        }
        if ($ToggleAdapter) {
            try {
                $didAnything = $true
                $radios = Get-PnpDevice -Class Bluetooth -ErrorAction SilentlyContinue | Where-Object { $_.FriendlyName -match 'radio|adapter' -or $_.Name -match 'radio|adapter' }
                if (-not $radios) { Emit-Event -Step 'reset' -Message 'No Bluetooth radios found to toggle.' -Level 'error' }
                else {
                    Emit-Event -Step 'reset' -Message ('Disabling Bluetooth radio(s)...')
                    foreach ($r in $radios) { try { Disable-PnpDevice -InstanceId $r.InstanceId -Confirm:$false -ErrorAction SilentlyContinue | Out-Null } catch {} }
                    $deadline1 = (Get-Date).AddSeconds(10)
                    do {
                        $allDown = $true
                        foreach ($r in $radios) {
                            $cur = Get-PnpDevice -InstanceId $r.InstanceId -ErrorAction SilentlyContinue
                            if ($cur -and $cur.Status -eq 'OK') { $allDown = $false; break }
                        }
                        if ($allDown) { break }
                        Start-Sleep -Milliseconds 500
                    } while ((Get-Date) -lt $deadline1)
                    Start-Sleep -Seconds 2
                    Emit-Event -Step 'reset' -Message 'Re-enabling Bluetooth radio(s)...'
                    foreach ($r in $radios) { try { Enable-PnpDevice -InstanceId $r.InstanceId -Confirm:$false -ErrorAction SilentlyContinue | Out-Null } catch {} }
                    $deadline2 = (Get-Date).AddSeconds(25)
                    do {
                        $allOk = $true
                        foreach ($r in $radios) {
                            $cur = Get-PnpDevice -InstanceId $r.InstanceId -ErrorAction SilentlyContinue
                            if (-not $cur -or $cur.Status -ne 'OK') { $allOk = $false; break }
                        }
                        if ($allOk) { break }
                        Start-Sleep -Milliseconds 700
                    } while ((Get-Date) -lt $deadline2)
                    if ($allOk) { Emit-Event -Step 'reset' -Message 'Bluetooth radio(s) re-enabled.' -Level 'success' }
                    else { Emit-Event -Step 'reset' -Message 'Bluetooth radio(s) did not report OK in time.' -Level 'error' }
                }
            } catch { Emit-Event -Step 'reset' -Message ('Exception while toggling adapter: ' + $_.Exception.Message) -Level 'error' }
        }
        if ($didAnything) {
            try {
                Emit-Event -Step 'reset' -Message 'Stabilizing Bluetooth stack...'
                $deadline3 = (Get-Date).AddSeconds(20)
                $ready = $false
                do {
                    try {
                        $op = [Windows.Devices.Enumeration.DeviceInformation]::FindAllAsync('System.Devices.Aep.ProtocolId:="{e0cbf06c-cd8b-4647-bb8a-263b43f0f974}"')
                        $ok = (Wait-AsyncResult -Operation $op -MaxSeconds 10)
                        $ready = $ok -ne $null
                    } catch { $ready = $false }
                    if ($ready) { break }
                    Start-Sleep -Milliseconds 800
                } while ((Get-Date) -lt $deadline3)
                if ($ready) { Emit-Event -Step 'reset' -Message 'Bluetooth stack ready.' -Level 'success' } else { Emit-Event -Step 'reset' -Message 'Bluetooth stack not ready yet; continuing.' -Level 'error' }
            } catch {}
        }
        return $didAnything
    }

    Log-Event -Step 'init' -Message ("Starting repair for '" + $DeviceName + "'. Options: RestartService=" + [bool]$RestartService + ", ToggleAdapter=" + [bool]$ToggleAdapter)

    $didReset = Restart-BluetoothStackIfRequested -RestartService:$RestartService -ToggleAdapter:$ToggleAdapter
    if ($didReset) { Log-Event -Step 'reset' -Message 'Bluetooth stack actions performed (service restart and/or adapter toggle).'} else { Log-Event -Step 'reset' -Message 'No Bluetooth stack changes requested.' }

    $pattern = if ($DeviceName -like '*') { $DeviceName } else { "*${DeviceName}*" }
    $devices = $null
    try { $devices = Get-AllBluetoothDevices } catch {}
    $matches = @()
    Log-Event -Step 'discover' -Message 'Scanning available devices...'
    if ($devices) { $matches = @($devices | Where-Object { $_.Name -like $pattern }) }

    if (-not $matches -or $matches.Count -eq 0) {
        Log-Event -Step 'discover' -Message ("Not found yet; waiting up to " + $TimeoutSeconds + "s for device to appear...")
        $waitedDevice = Wait-ForAepDevice -Address $null -Pattern $pattern -TimeoutSeconds $TimeoutSeconds
        if ($waitedDevice) { $matches = @($waitedDevice) }
    }

    if (-not $matches -or $matches.Count -eq 0) {
        $pairedEndpoints = Get-PairedAepDevices
        if ($pairedEndpoints) { $matches = @($pairedEndpoints | Where-Object { $_.Name -like $pattern }) }
        if (-not $matches -or $matches.Count -eq 0) {
            $reg = Get-RegistryPairedBluetoothDevices | Where-Object { $_.name -like $pattern }
            if ($reg) {
                $address = ($reg | Select-Object -First 1).address
                Log-Event -Step 'cleanup' -Message ("Removing registry and PnP entries for address " + $address)
                $cleanup = Remove-BluetoothDeviceByRegistry -Address $address -NamePattern $pattern
                Log-Event -Step 'wait' -Message 'Waiting for device to appear after cleanup...'
                $device = Wait-ForAepDevice -Address $address -Pattern $null -TimeoutSeconds $TimeoutSeconds
                if (-not $device) {
                    Log-Event -Step 'pairByAddress' -Message 'Attempting direct pairing by MAC address...'
                    $pairInfo = Pair-ByAddress -Address $address
                    if ($pairInfo -and ($pairInfo.status -eq 'Paired' -or $pairInfo.status -eq 'AlreadyPaired')) {
                        Log-Event -Step 'pairByAddress' -Message ("Pair result: " + $pairInfo.status) -Level 'success'
                        $out = [PSCustomObject]@{
                            ok = $true
                            step = 'pairByAddress'
                            resultStatus = $pairInfo.status
                            name = $pairInfo.name
                            address = $address
                            wasPaired = $true
                            wasConnected = $null
                            isPaired = $pairInfo.isPaired
                            isConnected = $null
                            resetPerformed = $didReset
                            cleanup = $cleanup
                        }
                        $out | ConvertTo-Json -Compress
                        exit 0
                    } else {
                        Log-Event -Step 'pairByAddress' -Message 'Direct pairing by MAC failed or device not reachable.' -Level 'error'
                        $out = [PSCustomObject]@{
                            ok = $false
                            step = 'pairByAddress'
                            message = 'Direct pairing by MAC failed or device not reachable.'
                            address = $address
                            resetPerformed = $didReset
                            cleanup = $cleanup
                        }
                        $out | ConvertTo-Json -Compress
                        exit 2
                    }
                }
                Log-Event -Step 'discover' -Message ("Device discovered by address: " + $device.Name)
                $matches = @($device)
            }
        }
        if (-not $matches -or $matches.Count -eq 0) {
            Log-Event -Step 'discover' -Message 'Device not found.' -Level 'error'
            $out = [PSCustomObject]@{
                ok = $false
                step = 'discover'
                message = 'Device not found. Ensure it is in pairing mode and within range.'
                resetPerformed = $didReset
            }
            $out | ConvertTo-Json -Compress
            exit 1
        }
    }

    $device = $matches | Sort-Object { $_.Properties['System.Devices.Aep.SignalStrength'] } -Descending | Select-Object -First 1

    $address = $device.Properties['System.Devices.Aep.DeviceAddress']
    $initialPaired = $device.Pairing.IsPaired
    $initialConnected = $device.Properties['System.Devices.Aep.IsConnected']

    if ($device.Pairing.IsPaired) {
        try {
            Log-Event -Step 'unpair' -Message 'Unpairing existing pairing...'
            $unpairOp = $device.Pairing.UnpairAsync()
            $unpairResult = Wait-AsyncResult -Operation $unpairOp
            Start-Sleep -Milliseconds 750
        } catch {}
    }

    Log-Event -Step 'rediscover' -Message 'Waiting for device to reappear...'
    $device = Wait-ForAepDevice -Address $address -Pattern $pattern -TimeoutSeconds $TimeoutSeconds
    if (-not $device) {
        Log-Event -Step 'rediscover' -Message 'Device disappeared during repair; still not discoverable.' -Level 'error'
        $out = [PSCustomObject]@{
            ok = $false
            step = 'rediscover'
            message = 'Device disappeared during repair; still not discoverable.'
            resetPerformed = $didReset
        }
        $out | ConvertTo-Json -Compress
        exit 1
    }

    Log-Event -Step 'pair' -Message 'Attempting to pair...'
    $pairOp = $device.Pairing.PairAsync()
    $pairResult = Wait-AsyncResult -Operation $pairOp

    Start-Sleep -Seconds 1
    $final = Wait-ForAepDevice -Address $address -Pattern $pattern -TimeoutSeconds 10

    $ok = $false
    if ($pairResult -and ($pairResult.Status -eq 'Paired' -or $pairResult.Status -eq 'AlreadyPaired')) { $ok = $true }

    $out = [PSCustomObject]@{
        ok = $ok
        step = 'pair'
        resultStatus = ($pairResult.Status)
        name = $final.Name
        address = $address
        wasPaired = $initialPaired
        wasConnected = $initialConnected
        isPaired = $final.Pairing.IsPaired
        isConnected = $final.Properties['System.Devices.Aep.IsConnected']
        resetPerformed = $didReset
    }
    if ($ok) { Log-Event -Step 'pair' -Message ("Pair result: " + $pairResult.Status) -Level 'success' } else { Log-Event -Step 'pair' -Message ("Pair result: " + $pairResult.Status) -Level 'error' }
    $out | ConvertTo-Json -Compress
    if ($ok) { exit 0 } else { exit 2 }
    ''')
)

# Enhanced troubleshooting script
PS_TROUBLESHOOT_SCRIPT = PS_SHARED_FUNCS + dedent(
    r'''
    function Test-BluetoothStack {
        Log-Event -Step 'test' -Message 'Testing Bluetooth stack components...'

        # Test Bluetooth service
        try {
            $btService = Get-Service -Name 'bthserv' -ErrorAction Stop
            Log-Event -Step 'test' -Message ("Bluetooth service status: " + $btService.Status)
        } catch {
            Log-Event -Step 'test' -Message 'Bluetooth service not found or accessible' -Level 'error'
        }

        # Test Bluetooth adapters
        try {
            $adapters = Get-PnpDevice -Class Bluetooth -ErrorAction SilentlyContinue | Where-Object { $_.FriendlyName -match 'adapter|radio' }
            if ($adapters) {
                foreach ($adapter in $adapters) {
                    Log-Event -Step 'test' -Message ("Adapter: " + $adapter.FriendlyName + " - Status: " + $adapter.Status)
                }
            } else {
                Log-Event -Step 'test' -Message 'No Bluetooth adapters found' -Level 'error'
            }
        } catch {
            Log-Event -Step 'test' -Message 'Failed to enumerate Bluetooth adapters' -Level 'error'
        }

        # Test WinRT Bluetooth APIs
        try {
            Initialize-WinRT
            $selector = 'System.Devices.Aep.ProtocolId:="{e0cbf06c-cd8b-4647-bb8a-263b43f0f974}"'
            $op = [Windows.Devices.Enumeration.DeviceInformation]::FindAllAsync($selector)
            $devices = Wait-AsyncResult -Operation $op -MaxSeconds 10
            if ($devices) {
                Log-Event -Step 'test' -Message ("WinRT API working - found " + $devices.Count + " devices")
            } else {
                Log-Event -Step 'test' -Message 'WinRT API not returning devices' -Level 'error'
            }
        } catch {
            Log-Event -Step 'test' -Message ('WinRT API test failed: ' + $_.Exception.Message) -Level 'error'
        }

        # Test registry access
        try {
            $regPath = 'HKLM:\SYSTEM\CurrentControlSet\Services\BTHPORT\Parameters\Devices'
            $regDevices = Get-ChildItem -Path $regPath -ErrorAction Stop
            Log-Event -Step 'test' -Message ("Registry access OK - found " + $regDevices.Count + " paired devices")
        } catch {
            Log-Event -Step 'test' -Message 'Registry access failed' -Level 'error'
        }
    }

    function Clear-BluetoothCache {
        Log-Event -Step 'clean' -Message 'Clearing Bluetooth cache and temporary files...'

        # Stop Bluetooth service
        try {
            Stop-Service -Name 'bthserv' -Force -ErrorAction Stop
            Log-Event -Step 'clean' -Message 'Bluetooth service stopped'
        } catch {
            Log-Event -Step 'clean' -Message 'Failed to stop Bluetooth service' -Level 'error'
        }

        # Clear Bluetooth cache directories
        $cachePaths = @(
            "$env:LOCALAPPDATA\Microsoft\Windows\Bluetooth",
            "$env:PROGRAMDATA\Microsoft\Windows\Bluetooth",
            "$env:WINDIR\System32\config\systemprofile\AppData\Local\Microsoft\Windows\Bluetooth"
        )

        foreach ($path in $cachePaths) {
            if (Test-Path $path) {
                try {
                    Remove-Item -Path "$path\*" -Recurse -Force -ErrorAction SilentlyContinue
                    Log-Event -Step 'clean' -Message ("Cleared cache: " + $path)
                } catch {
                    Log-Event -Step 'clean' -Message ("Failed to clear: " + $path) -Level 'error'
                }
            }
        }

        # Restart Bluetooth service
        try {
            Start-Service -Name 'bthserv' -ErrorAction Stop
            Log-Event -Step 'clean' -Message 'Bluetooth service restarted'
        } catch {
            Log-Event -Step 'clean' -Message 'Failed to restart Bluetooth service' -Level 'error'
        }
    }

    Test-BluetoothStack
    Log-Event -Step 'complete' -Message 'Troubleshooting complete'
    '''
)

# Advanced repair scripts
PS_RESET_STACK_SCRIPT = dedent(r'''
    $script:EMIT_EVENTS_TO_STDERR = $true
    ''') + PS_SHARED_FUNCS + dedent(r'''
    function Reset-BluetoothStack {
        Log-Event -Step 'stack-reset' -Message 'Resetting entire Bluetooth stack...'

        # Stop all Bluetooth-related services
        $services = @('bthserv', 'BthAvctpSvc', 'BTHPORT', 'BthHFSrv')
        foreach ($service in $services) {
            try {
                $svc = Get-Service -Name $service -ErrorAction SilentlyContinue
                if ($svc -and $svc.Status -eq 'Running') {
                    Stop-Service -Name $service -Force -ErrorAction Stop
                    Log-Event -Step 'stack-reset' -Message "Stopped service: $service"
                }
            } catch {
                Log-Event -Step 'stack-reset' -Message "Failed to stop service: $service" -Level 'error'
            }
        }

        # Reset Bluetooth registry keys
        try {
            $regPaths = @(
                'HKLM:\SYSTEM\CurrentControlSet\Services\BTHPORT\Parameters\Keys',
                'HKLM:\SYSTEM\CurrentControlSet\Services\BTHPORT\Parameters\Devices'
            )
            foreach ($path in $regPaths) {
                if (Test-Path $path) {
                    # Backup before clearing
                    $backupPath = $path + "_backup_" + (Get-Date -Format "yyyyMMdd_HHmmss")
                    Copy-Item -Path $path -Destination $backupPath -Recurse -ErrorAction SilentlyContinue
                    Log-Event -Step 'stack-reset' -Message "Registry backed up to: $backupPath"
                }
            }
        } catch {
            Log-Event -Step 'stack-reset' -Message 'Registry backup failed' -Level 'error'
        }

        # Clear Windows Bluetooth cache
        $cachePaths = @(
            "$env:LOCALAPPDATA\Microsoft\Windows\Bluetooth",
            "$env:PROGRAMDATA\Microsoft\Windows\Bluetooth",
            "$env:WINDIR\System32\config\systemprofile\AppData\Local\Microsoft\Windows\Bluetooth",
            "$env:WINDIR\ServiceProfiles\LocalService\AppData\Local\Microsoft\Windows\Bluetooth"
        )

        foreach ($path in $cachePaths) {
            if (Test-Path $path) {
                try {
                    Remove-Item -Path "$path\*" -Recurse -Force -ErrorAction SilentlyContinue
                    Log-Event -Step 'stack-reset' -Message "Cleared cache: $path"
                } catch {
                    Log-Event -Step 'stack-reset' -Message "Failed to clear: $path" -Level 'error'
                }
            }
        }

        # Reset network stack components
        try {
            netsh winsock reset | Out-Null
            Log-Event -Step 'stack-reset' -Message 'Winsock stack reset'
        } catch {
            Log-Event -Step 'stack-reset' -Message 'Winsock reset failed' -Level 'error'
        }

        # Restart services
        foreach ($service in $services) {
            try {
                $svc = Get-Service -Name $service -ErrorAction SilentlyContinue
                if ($svc) {
                    Start-Service -Name $service -ErrorAction Stop
                    Log-Event -Step 'stack-reset' -Message "Restarted service: $service"
                }
            } catch {
                Log-Event -Step 'stack-reset' -Message "Failed to restart service: $service" -Level 'error'
            }
        }

        Log-Event -Step 'stack-reset' -Message 'Bluetooth stack reset completed'
    }

    Reset-BluetoothStack

    $result = @{
        ok = $true
        message = "Bluetooth stack reset completed"
        step = "stack-reset"
    }
    $result | ConvertTo-Json -Compress
''')

PS_RESET_DRIVERS_SCRIPT = dedent(r'''
    $script:EMIT_EVENTS_TO_STDERR = $true
    ''') + PS_SHARED_FUNCS + dedent(r'''
    function Reset-BluetoothDrivers {
        Log-Event -Step 'driver-reset' -Message 'Resetting Bluetooth drivers...'

        # Get all Bluetooth devices
        try {
            $btDevices = Get-PnpDevice -Class Bluetooth -ErrorAction SilentlyContinue
            $btAdapters = $btDevices | Where-Object { $_.FriendlyName -match 'adapter|radio|controller' }

            foreach ($adapter in $btAdapters) {
                try {
                    Log-Event -Step 'driver-reset' -Message "Disabling adapter: $($adapter.FriendlyName)"
                    Disable-PnpDevice -InstanceId $adapter.InstanceId -Confirm:$false -ErrorAction Stop
                    Start-Sleep -Seconds 3

                    Log-Event -Step 'driver-reset' -Message "Re-enabling adapter: $($adapter.FriendlyName)"
                    Enable-PnpDevice -InstanceId $adapter.InstanceId -Confirm:$false -ErrorAction Stop
                    Start-Sleep -Seconds 2
                } catch {
                    Log-Event -Step 'driver-reset' -Message "Failed to reset adapter: $($adapter.FriendlyName)" -Level 'error'
                }
            }

            # Force driver reinstall for problematic devices
            $problemDevices = Get-PnpDevice -Class Bluetooth | Where-Object { $_.Status -ne 'OK' }
            foreach ($device in $problemDevices) {
                try {
                    Log-Event -Step 'driver-reset' -Message "Reinstalling driver for: $($device.FriendlyName)"
                    pnputil /remove-device $device.InstanceId /force | Out-Null
                    pnputil /scan-hardware | Out-Null
                } catch {
                    Log-Event -Step 'driver-reset' -Message "Failed to reinstall driver for: $($device.FriendlyName)" -Level 'error'
                }
            }

        } catch {
            Log-Event -Step 'driver-reset' -Message 'Failed to enumerate Bluetooth devices' -Level 'error'
        }

        Log-Event -Step 'driver-reset' -Message 'Bluetooth driver reset completed'
    }

    Reset-BluetoothDrivers

    $result = @{
        ok = $true
        message = "Bluetooth drivers reset completed"
        step = "driver-reset"
    }
    $result | ConvertTo-Json -Compress
''')

PS_LIST_SCRIPT = dedent(
    r'''
    param([int]$TimeoutSeconds = 30)
    $script:EMIT_EVENTS_TO_STDERR = $true
    ''') + PS_SHARED_FUNCS + dedent(r'''

    function Get-PairedDevices {
        Initialize-WinRT
        $selector = 'System.Devices.Aep.ProtocolId:="{e0cbf06c-cd8b-4647-bb8a-263b43f0f974}" AND System.Devices.Aep.IsPaired:=System.StructuredQueryType.Boolean#True'
        $props = @('System.Devices.Aep.DeviceAddress','System.Devices.Aep.IsPaired','System.Devices.Aep.IsConnected','System.Devices.Aep.SignalStrength')
        $devs = $null
        try { $devs = Wait-AsyncResult -Operation ([Windows.Devices.Enumeration.DeviceInformation]::FindAllAsync($selector, $props)) } catch { $devs = Wait-AsyncResult -Operation ([Windows.Devices.Enumeration.DeviceInformation]::FindAllAsync($selector)) }
        $list = @()
        foreach ($d in $devs) {
            $list += [PSCustomObject]@{
                name = $d.Name
                address = $d.Properties['System.Devices.Aep.DeviceAddress']
                isPaired = $true
                isConnected = $d.Properties['System.Devices.Aep.IsConnected']
                signal = $d.Properties['System.Devices.Aep.SignalStrength']
            }
        }
        return $list
    }

    function Get-PnpBluetoothNames {
        $pnps = Get-PnpDevice -Class Bluetooth -ErrorAction SilentlyContinue
        $list = @()
        foreach ($p in $pnps) {
            $n = if ($p.FriendlyName) { $p.FriendlyName } elseif ($p.Name) { $p.Name } else { $null }
            if (-not $n) { continue }
            $lower = $n.ToLowerInvariant()
            if ($lower -match 'radio' -or $lower -match 'enumerator' -or $lower -match 'adapter' -or $lower -match 'protocol') { continue }
            $base = ($n -replace "\s*\(.*\)\s*$", '').Trim()
            if (-not [string]::IsNullOrWhiteSpace($base)) {
                $list += [PSCustomObject]@{ name = $base; address = $null; isPaired = $true; isConnected = $null; signal = $null }
            }
        }
        return $list
    }

    $map = @{}
    function Add-Or-Update($key, $obj) {
        if (-not $key) { $key = $obj.name }
        if ($map.ContainsKey($key)) {
            foreach ($k in $obj.PSObject.Properties.Name) {
                if ($null -ne $obj.$k) { $map[$key].$k = $obj.$k }
            }
        } else {
            $map[$key] = $obj
        }
    }

    $aep = @()
    try { $aep = Get-AllBluetoothDevices } catch {}
    Emit-Event -Step 'list' -Message ("AEP devices: " + ($aep.Count)) -Level 'info'
    foreach ($d in $aep) {
        $item = [PSCustomObject]@{
            name = $d.Name
            address = $d.Properties['System.Devices.Aep.DeviceAddress']
            isPaired = $d.Pairing.IsPaired
            isConnected = $d.Properties['System.Devices.Aep.IsConnected']
            signal = $d.Properties['System.Devices.Aep.SignalStrength']
        }
        Add-Or-Update ($item.address) $item
    }

    $paired = @()
    try { $paired = Get-PairedDevices } catch {}
    Emit-Event -Step 'list' -Message ("AEP paired: " + ($paired.Count)) -Level 'info'
    foreach ($p in $paired) { Add-Or-Update ($p.address) $p }

    $pnp = Get-PnpBluetoothNames
    Emit-Event -Step 'list' -Message ("PnP: " + ($pnp.Count)) -Level 'info'
    foreach ($q in $pnp) { Add-Or-Update ($q.name) $q }

    $reg = Get-RegistryPairedBluetoothDevices
    Emit-Event -Step 'list' -Message ("BTHPORT registry: " + ($reg.Count)) -Level 'info'
    foreach ($r in $reg) { Add-Or-Update ($r.address) $r }

    $items = $map.Values | Sort-Object -Property @{Expression='isPaired';Descending=$true}, @{Expression='signal';Descending=$true}, name
    $items | ConvertTo-Json -Compress
    exit 0
    '''
)


def repair_bluetooth_device(
    device_name: str,
    restart_service: bool = False,
    toggle_adapter: bool = False,
    timeout_seconds: int = 60,
    reset_stack: bool = False,
    clear_cache: bool = False,
    reset_drivers: bool = False,
) -> dict:
    """Attempt to repair a Bluetooth device by unpairing and re-pairing it."""
    logger = logging.getLogger("bt_repair")
    if not device_name:
        raise ValueError("Device name cannot be empty.")
    ps_args: List[str] = [
        "-DeviceName", device_name,
        "-TimeoutSeconds", str(timeout_seconds),
    ]
    if restart_service:
        ps_args.append("-RestartService")
    if toggle_adapter:
        ps_args.append("-ToggleAdapter")

    completed: Optional[subprocess.CompletedProcess] = None
    stdout = ""
    try:
        completed = run_powershell_script(PS_REPAIR_SCRIPT, ps_args, timeout=timeout_seconds + 15)
        stdout = (completed.stdout or "").strip()
        logger.info("repair_bluetooth_device stdout=%s", stdout[:2000])

        # Filter out event lines and extract the final JSON result
        data = {}
        if stdout:
            lines = stdout.splitlines()
            for line in reversed(lines):  # Start from the end to find the last JSON
                line = line.strip().lstrip("\ufeff")
                if line and not line.startswith("##EVT## "):
                    try:
                        data = json.loads(line)
                        break
                    except json.JSONDecodeError:
                        continue
        if not data:
            data = {"ok": False, "message": "No valid JSON result found"}
    except json.JSONDecodeError as e:
        logger.error("JSON decode error: %s\nRaw stdout: %s", e, stdout)
        data = {"ok": False, "message": "JSON parsing failed", "raw": stdout}
    except subprocess.TimeoutExpired:
        logger.error("PowerShell repair timed out after %ss", timeout_seconds + 15)
        data = {"ok": False, "message": f"Operation timed out after {timeout_seconds + 15} seconds. Device may not be in pairing mode or within range."}
    except Exception as e:
        logger.error("Exception in repair: %s\n%s", e, traceback.format_exc())
        data = {"ok": False, "message": f"Unexpected error: {str(e)}"}
        # Don't re-raise, return the error data instead

    if completed and completed.returncode not in (0, 1, 2):
        raise RuntimeError(
            f"PowerShell failed: {completed.returncode}\nSTDOUT: {stdout}\nSTDERR: {getattr(completed, 'stderr', '')}"
        )

    return data or {"ok": False, "message": "No data returned from PowerShell"}


def list_bluetooth_devices(timeout_seconds: int = 30) -> List[dict]:
    """Return a list of discoverable/paired Bluetooth devices with basic properties."""
    logger = logging.getLogger("bt_repair")
    stdout = ""
    try:
        completed = run_powershell_script(
            PS_LIST_SCRIPT, ["-TimeoutSeconds", str(timeout_seconds)], timeout=timeout_seconds + 10
        )
        stdout = (completed.stdout or "")
        logger.info("list_bluetooth_devices stdout=%s", stdout[:2000])

        # Filter out event lines/noise and keep the last JSON
        cleaned_lines = []
        for line in stdout.splitlines():
            s = line.strip().lstrip("\ufeff")
            if not s or s.startswith("##EVT## "):
                continue
            cleaned_lines.append(s)
        cleaned = "\n".join(cleaned_lines).strip()

        items = []
        try:
            if cleaned:
                items = json.loads(cleaned)
        except json.JSONDecodeError:
            # try last non-event line as JSON
            last = ""
            for s in reversed(cleaned_lines):
                if s:
                    last = s
                    break
            if last:
                items = json.loads(last)

        if isinstance(items, dict):
            items = [items]
        return list(items or [])
    except json.JSONDecodeError as e:
        logger.error("JSON decode error: %s\nRaw stdout: %s", e, stdout)
        return [{"name": "(error)", "isPaired": False, "isConnected": False, "signal": None, "raw": stdout}]
    except subprocess.TimeoutExpired:
        logger.error("PowerShell list timed out after %ss", timeout_seconds + 10)
        return [{"name": "(timeout)", "isPaired": False, "isConnected": False, "signal": None, "error": "Device listing timed out"}]
    except Exception as e:
        logger.error("Exception in list: %s\n%s", e, traceback.format_exc())
        return [{"name": "(error)", "isPaired": False, "isConnected": False, "signal": None, "error": str(e)}]


def run_troubleshooting() -> int:
    """Run comprehensive Bluetooth troubleshooting and diagnostics."""
    print("Running Bluetooth Troubleshooting and Diagnostics")
    print("=" * 60)

    logger = logging.getLogger("bt_repair")

    def on_event(evt: dict) -> None:
        step = evt.get('step', '')
        message = evt.get('message', '')
        level = evt.get('level', 'info')

        if level == 'error':
            print(f"❌ [{step}] {message}")
        elif level == 'success':
            print(f"✅ [{step}] {message}")
        else:
            print(f"ℹ️  [{step}] {message}")

    try:
        print("Running diagnostic tests...")
        completed, full_stdout = run_powershell_script_streaming(
            PS_TROUBLESHOOT_SCRIPT, [], on_event=on_event, timeout=60
        )

        if completed.returncode == 0:
            print("\n" + "=" * 60)
            print("Troubleshooting completed successfully!")
            print("\nRecommendations:")
            print("1. If WinRT API failed, try running as Administrator")
            print("2. If adapters show errors, update Bluetooth drivers")
            print("3. If service issues, restart Windows or run 'sfc /scannow'")
            print("4. Try the --deep-clean option for cache clearing")
            return 0
        else:
            print(f"\nTroubleshooting failed with return code: {completed.returncode}")
            return 1

    except Exception as e:
        print(f"Troubleshooting failed: {e}")
        logger.error("Troubleshooting exception: %s", e)
        return 1


def deep_clean_bluetooth() -> dict:
    """Perform deep Bluetooth cleanup including cache clearing and service restart."""
    logger = logging.getLogger("bt_repair")

    # Enhanced cleanup script
    cleanup_script = dedent(r'''
        $script:EMIT_EVENTS_TO_STDERR = $true
        ''') + PS_SHARED_FUNCS + dedent(r'''
        function Clear-BluetoothCache {
            Log-Event -Step 'clean' -Message 'Clearing Bluetooth cache and temporary files...'

            # Stop Bluetooth service
            try {
                Stop-Service -Name 'bthserv' -Force -ErrorAction Stop
                Log-Event -Step 'clean' -Message 'Bluetooth service stopped'
            } catch {
                Log-Event -Step 'clean' -Message 'Failed to stop Bluetooth service' -Level 'error'
            }

            # Clear Bluetooth cache directories
            $cachePaths = @(
                "$env:LOCALAPPDATA\Microsoft\Windows\Bluetooth",
                "$env:PROGRAMDATA\Microsoft\Windows\Bluetooth",
                "$env:WINDIR\System32\config\systemprofile\AppData\Local\Microsoft\Windows\Bluetooth"
            )

            foreach ($path in $cachePaths) {
                if (Test-Path $path) {
                    try {
                        Remove-Item -Path "$path\*" -Recurse -Force -ErrorAction SilentlyContinue
                        Log-Event -Step 'clean' -Message ("Cleared cache: " + $path)
                    } catch {
                        Log-Event -Step 'clean' -Message ("Failed to clear: " + $path) -Level 'error'
                    }
                }
            }

            # Restart Bluetooth service
            try {
                Start-Service -Name 'bthserv' -ErrorAction Stop
                Log-Event -Step 'clean' -Message 'Bluetooth service restarted'
            } catch {
                Log-Event -Step 'clean' -Message 'Failed to restart Bluetooth service' -Level 'error'
            }
        }

        Clear-BluetoothCache

        # Additional cleanup steps
        Log-Event -Step 'deep-clean' -Message 'Performing additional cleanup...'

        # Reset network adapters
        try {
            netsh winsock reset | Out-Null
            Log-Event -Step 'deep-clean' -Message 'Winsock reset completed'
        } catch {
            Log-Event -Step 'deep-clean' -Message 'Winsock reset failed' -Level 'error'
        }

        $result = @{
            ok = $true
            message = "Deep cleanup completed"
            step = "deep-clean"
        }
        $result | ConvertTo-Json -Compress
    ''')

    try:
        completed = run_powershell_script(cleanup_script, [], timeout=120)
        stdout = (completed.stdout or "").strip()

        # Parse result
        data = {}
        if stdout:
            lines = stdout.splitlines()
            for line in reversed(lines):
                line = line.strip().lstrip("\ufeff")
                if line and not line.startswith("##EVT## "):
                    try:
                        data = json.loads(line)
                        break
                    except json.JSONDecodeError:
                        continue

        if not data:
            data = {"ok": True, "message": "Deep cleanup completed", "step": "deep-clean"}

        return data

    except Exception as e:
        logger.error("Deep clean failed: %s", e)
        return {"ok": False, "message": f"Deep cleanup failed: {str(e)}", "step": "deep-clean"}


def reset_bluetooth_stack() -> dict:
    """Reset the entire Bluetooth stack including services and registry."""
    logger = logging.getLogger("bt_repair")

    try:
        completed = run_powershell_script(PS_RESET_STACK_SCRIPT, [], timeout=180)
        stdout = (completed.stdout or "").strip()

        # Parse result
        data = {}
        if stdout:
            lines = stdout.splitlines()
            for line in reversed(lines):
                line = line.strip().lstrip("\ufeff")
                if line and not line.startswith("##EVT## "):
                    try:
                        data = json.loads(line)
                        break
                    except json.JSONDecodeError:
                        continue

        if not data:
            data = {"ok": True, "message": "Bluetooth stack reset completed", "step": "stack-reset"}

        return data

    except Exception as e:
        logger.error("Stack reset failed: %s", e)
        return {"ok": False, "message": f"Stack reset failed: {str(e)}", "step": "stack-reset"}


def reset_bluetooth_drivers() -> dict:
    """Reset Bluetooth drivers and reinitialize adapters."""
    logger = logging.getLogger("bt_repair")

    try:
        completed = run_powershell_script(PS_RESET_DRIVERS_SCRIPT, [], timeout=120)
        stdout = (completed.stdout or "").strip()

        # Parse result
        data = {}
        if stdout:
            lines = stdout.splitlines()
            for line in reversed(lines):
                line = line.strip().lstrip("\ufeff")
                if line and not line.startswith("##EVT## "):
                    try:
                        data = json.loads(line)
                        break
                    except json.JSONDecodeError:
                        continue

        if not data:
            data = {"ok": True, "message": "Bluetooth drivers reset completed", "step": "driver-reset"}

        return data

    except Exception as e:
        logger.error("Driver reset failed: %s", e)
        return {"ok": False, "message": f"Driver reset failed: {str(e)}", "step": "driver-reset"}


def clear_bluetooth_cache() -> dict:
    """Clear all Bluetooth cache and temporary files."""
    return deep_clean_bluetooth()  # Same functionality


class RepairApp:
    def __init__(self) -> None:
        if tk is None:
            raise RuntimeError("Tkinter is not available in this Python environment.")
        self.root = tk.Tk()
        self.root.title("Bluetooth Repair")
        self.root.minsize(560, 300)

        self.device_var = tk.StringVar()
        self.restart_var = tk.BooleanVar(value=False)
        self.toggle_var = tk.BooleanVar(value=False)
        self.reset_stack_var = tk.BooleanVar(value=False)
        self.clear_cache_var = tk.BooleanVar(value=False)
        self.reset_drivers_var = tk.BooleanVar(value=False)
        self.aggressive_var = tk.BooleanVar(value=False)
        self.status_var = tk.StringVar(value="Ready")
        self.progress_var = tk.DoubleVar(value=0.0)
        self.admin_var = tk.StringVar(value=("Administrator" if is_user_admin() else "Standard user"))
        self.log_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'bluetooth_repair.log')
        self.devices: List[dict] = []
        self._display_to_name: dict[str, str] = {}
        self._max_step_seen: int = -1
        self._step_sequence: List[str] = [
            'init', 'reset', 'discover', 'cleanup', 'wait', 'unpair', 'rediscover', 'pair', 'final'
        ]

        container = ttk.Frame(self.root, padding=12)
        container.pack(fill=tk.BOTH, expand=True)

        row = 0
        ttk.Label(container, text="Privilege:").grid(row=row, column=0, sticky="w")
        ttk.Label(container, textvariable=self.admin_var).grid(row=row, column=1, sticky="w")
        self.admin_btn = ttk.Button(container, text="Relaunch as Admin…", command=self.relaunch_admin)
        self.admin_btn.grid(row=row, column=2, sticky="e")
        container.columnconfigure(1, weight=1)

        row += 1
        ttk.Label(container, text="Device:").grid(row=row, column=0, sticky="w")
        self.device_combo = ttk.Combobox(container, textvariable=self.device_var, width=52)
        self.device_combo.grid(row=row, column=1, sticky="ew", padx=(6, 6))
        self.scan_btn = ttk.Button(container, text="Scan", command=self.scan_devices)
        self.scan_btn.grid(row=row, column=2, sticky="e")

        row += 1
        self.restart_chk = ttk.Checkbutton(container, text="Restart Bluetooth service (admin)", variable=self.restart_var)
        self.restart_chk.grid(row=row, column=1, sticky="w", pady=(8, 0))

        row += 1
        self.toggle_chk = ttk.Checkbutton(container, text="Toggle adapter (admin)", variable=self.toggle_var)
        self.toggle_chk.grid(row=row, column=1, sticky="w")

        # Advanced repair options
        row += 1
        ttk.Label(container, text="Advanced Options:", font=("TkDefaultFont", 9, "bold")).grid(row=row, column=1, sticky="w", pady=(8, 2))

        row += 1
        self.reset_stack_chk = ttk.Checkbutton(container, text="Reset Bluetooth stack (admin)", variable=self.reset_stack_var)
        self.reset_stack_chk.grid(row=row, column=1, sticky="w")

        row += 1
        self.clear_cache_chk = ttk.Checkbutton(container, text="Clear cache & temp files (admin)", variable=self.clear_cache_var)
        self.clear_cache_chk.grid(row=row, column=1, sticky="w")

        row += 1
        self.reset_drivers_chk = ttk.Checkbutton(container, text="Reset drivers & adapters (admin)", variable=self.reset_drivers_var)
        self.reset_drivers_chk.grid(row=row, column=1, sticky="w")

        row += 1
        self.aggressive_chk = ttk.Checkbutton(container, text="🚀 Aggressive mode (all methods)", variable=self.aggressive_var,
                                            command=self.on_aggressive_changed)
        self.aggressive_chk.grid(row=row, column=1, sticky="w", pady=(4, 0))

        row += 1
        self.repair_btn = ttk.Button(container, text="Repair", command=self.repair_selected)
        self.repair_btn.grid(row=row, column=2, sticky="e", pady=(10, 0))

        # Add troubleshoot button
        self.troubleshoot_btn = ttk.Button(container, text="Troubleshoot", command=self.run_troubleshoot)
        self.troubleshoot_btn.grid(row=row, column=1, sticky="w", pady=(10, 0))

        row += 1
        ttk.Separator(container).grid(row=row, column=0, columnspan=3, sticky="ew", pady=(12, 8))

        row += 1
        ttk.Label(container, text="Progress:").grid(row=row, column=0, sticky="w")
        self.progress = ttk.Progressbar(container, variable=self.progress_var, maximum=100)
        self.progress.grid(row=row, column=1, columnspan=2, sticky="ew")

        row += 1
        ttk.Label(container, textvariable=self.status_var).grid(row=row, column=0, columnspan=3, sticky="w")

        row += 1
        log_frame = ttk.Frame(container)
        log_frame.grid(row=row, column=0, columnspan=3, sticky="nsew", pady=(6, 0))
        container.rowconfigure(row, weight=1)
        container.columnconfigure(1, weight=1)

        self.log_text = tk.Text(log_frame, height=10, wrap="word", state="disabled")
        self.log_scroll = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=self.log_scroll.set)
        self.log_text.grid(row=0, column=0, sticky="nsew")
        self.log_scroll.grid(row=0, column=1, sticky="ns")
        log_frame.rowconfigure(0, weight=1)
        log_frame.columnconfigure(0, weight=1)
        try:
            self.log_text.tag_configure('info', foreground='#1f4b99')
            self.log_text.tag_configure('success', foreground='#0a7d00')
            self.log_text.tag_configure('error', foreground='#a40000')
        except Exception:
            pass

        row += 1
        ttk.Label(container, text=f"Log: {self.log_path}").grid(row=row, column=0, columnspan=3, sticky="w")

        self.root.after(200, self.scan_devices)

    def relaunch_admin(self) -> None:
        if is_user_admin():
            messagebox.showinfo("Bluetooth Repair", "Already running as Administrator.")
            return
        if relaunch_as_admin_if_needed(True):
            self.root.quit()

    def set_busy(self, busy: bool) -> None:
        state = tk.DISABLED if busy else tk.NORMAL
        for w in (self.device_combo, self.scan_btn, self.restart_chk, self.toggle_chk, self.repair_btn, self.admin_btn):
            w.configure(state=state)

    def clear_log(self) -> None:
        try:
            self.log_text.configure(state="normal")
            self.log_text.delete("1.0", tk.END)
            self.log_text.configure(state="disabled")
        except Exception:
            pass

    def append_log(self, message: str, level: str = 'info', step: Optional[str] = None) -> None:
        prefix = f"[{step}] " if step else ""
        try:
            self.log_text.configure(state="normal")
            self.log_text.insert(tk.END, prefix + message + "\n", (level,))
            self.log_text.see(tk.END)
        finally:
            self.log_text.configure(state="disabled")

    def _update_progress_for_step(self, step: str) -> None:
        if step not in self._step_sequence:
            return
        idx = self._step_sequence.index(step)
        if idx <= self._max_step_seen:
            return
        self._max_step_seen = idx
        percent = (idx / (len(self._step_sequence) - 1)) * 100.0
        self.progress_var.set(percent)

    def format_device(self, item: dict) -> str:
        name = item.get("name", "Unknown")
        parts = []
        if item.get("isPaired"): parts.append("Paired")
        if item.get("isConnected"): parts.append("Connected")
        signal = item.get("signal")
        if signal is not None: parts.append(f"Signal: {signal}dBm")
        return f"{name} ({', '.join(parts)})" if parts else name

    def scan_devices(self) -> None:
        self.status_var.set("Scanning for Bluetooth devices...")
        self.set_busy(True)
        try:
            self.progress.configure(mode="indeterminate")
            self.progress.start(12)
        except Exception:
            pass

        def work():
            devices = list_bluetooth_devices()
            paired = [i for i in devices if i.get("isPaired")]
            unpaired = [i for i in devices if not i.get("isPaired")]
            ordered = paired + unpaired
            self.devices = ordered

            def done():
                values = [self.format_device(item) for item in ordered]
                self._display_to_name = {self.format_device(item): item.get("name", "") for item in ordered}
                self.device_combo["values"] = values
                if ordered and not self.device_var.get():
                    self.device_var.set(values[0])
                msg = "Scan complete"
                if paired:   msg += f" — {len(paired)} paired"
                if unpaired: msg += f", {len(unpaired)} unpaired"
                if not ordered:
                    msg = "No devices found. Put device in pairing mode and retry."
                self.status_var.set(msg)
                self.set_busy(False)
                try:
                    self.progress.stop()
                    self.progress.configure(mode="determinate")
                    self.progress_var.set(0)
                except Exception:
                    pass

            self.root.after(0, done)

        threading.Thread(target=work, daemon=True).start()

    def repair_selected(self) -> None:
        selected = self.device_var.get().strip()
        if not selected:
            messagebox.showwarning("Bluetooth Repair", "Please enter or select a device name.")
            return
        name = self._display_to_name.get(selected, selected.split(" (")[0])

        # Check if any admin-required options are selected
        needs_admin = (self.restart_var.get() or self.toggle_var.get() or
                      self.reset_stack_var.get() or self.clear_cache_var.get() or
                      self.reset_drivers_var.get() or self.aggressive_var.get())

        if needs_admin and not is_user_admin():
            if messagebox.askyesno("Administrator Required", "Admin is required for selected options. Relaunch as Administrator now?"):
                if relaunch_as_admin_if_needed(True):
                    self.root.quit()
                return

        if messagebox.askyesno("Confirm Repair", f"Repair '{name}'? This will unpair and re-pair the device."):
            self.status_var.set(f"Repairing '{name}'...")
            self.set_busy(True)
            self.clear_log()
            self._max_step_seen = -1
            self._saw_first_event = False
            try:
                self.progress.configure(mode="indeterminate")
                self.progress.start(12)
            except Exception:
                pass

            def on_evt(evt: dict) -> None:
                step = str(evt.get('step') or '')
                msg = str(evt.get('message') or '')
                level = str(evt.get('level') or 'info')
                def ui():
                    if not getattr(self, "_saw_first_event", False):
                        try:
                            self.progress.stop()
                            self.progress.configure(mode="determinate")
                        except Exception:
                            pass
                        self._saw_first_event = True
                    self.append_log(msg, level=level, step=step)
                    self.status_var.set(msg or self.status_var.get())
                    if step:
                        self._update_progress_for_step(step)
                self.root.after(0, ui)

            def work():
                # Handle aggressive mode
                if self.aggressive_var.get():
                    self.restart_var.set(True)
                    self.toggle_var.set(True)
                    self.reset_stack_var.set(True)
                    self.clear_cache_var.set(True)
                    self.reset_drivers_var.set(True)

                # Execute advanced repair operations first
                advanced_operations = []
                if self.clear_cache_var.get():
                    advanced_operations.append(("Clearing Bluetooth cache", clear_bluetooth_cache))
                if self.reset_drivers_var.get():
                    advanced_operations.append(("Resetting Bluetooth drivers", reset_bluetooth_drivers))
                if self.reset_stack_var.get():
                    advanced_operations.append(("Resetting Bluetooth stack", reset_bluetooth_stack))

                # Execute advanced operations
                for operation_name, operation_func in advanced_operations:
                    try:
                        self.root.after(0, lambda op=operation_name: self.status_var.set(f"🔧 {op}..."))
                        result = operation_func()
                        if result.get("ok"):
                            self.root.after(0, lambda op=operation_name: self.append_log(f"✅ {op} completed successfully", level="success"))
                        else:
                            self.root.after(0, lambda op=operation_name, msg=result.get('message', 'Unknown error'):
                                          self.append_log(f"❌ {op} failed: {msg}", level="error"))
                    except Exception as e:
                        self.root.after(0, lambda op=operation_name, err=str(e):
                                      self.append_log(f"❌ {op} failed: {err}", level="error"))

                # Now proceed with standard repair
                ps_args: List[str] = ["-DeviceName", name, "-TimeoutSeconds", str(60)]
                if self.restart_var.get(): ps_args.append("-RestartService")
                if self.toggle_var.get():  ps_args.append("-ToggleAdapter")

                try:
                    completed, full_stdout = run_powershell_script_streaming(PS_REPAIR_SCRIPT, ps_args, on_event=on_evt, timeout=75)
                    result: dict = {}
                    for raw in full_stdout.splitlines():
                        s = raw.strip()
                        if not s or s.startswith('##EVT## '): continue
                        try:
                            obj = json.loads(s)
                            if isinstance(obj, dict): result = obj
                        except Exception:
                            continue
                    if not result:
                        if completed.returncode == -9:
                            result = {"ok": False, "message": "Operation timed out. Device may not be in pairing mode or within range."}
                        elif completed.returncode not in (0,1,2):
                            result = {"ok": False, "message": f"PowerShell process failed with code {completed.returncode}"}
                        else:
                            result = {"ok": False, "message": "No result returned from repair operation"}
                except Exception as exc:
                    result = {"ok": False, "message": str(exc)}

                def done():
                    try:
                        self.progress.stop()
                        self.progress.configure(mode="determinate")
                    except Exception:
                        pass
                    self.progress_var.set(100)
                    if result.get("ok"):
                        self.append_log("Repair succeeded.", level='success', step='final')
                        self.status_var.set(f"Success: '{result.get('name') or name}' paired. Connected={result.get('isConnected')}")
                    else:
                        msg = result.get("message") or f"Pairing failed with status {result.get('resultStatus')}"
                        self.append_log("Repair failed: " + msg, level='error', step='final')
                        self.status_var.set(f"Failed: {msg}")
                        messagebox.showerror("Bluetooth Repair", self.status_var.get())
                    self.set_busy(False)
                    self.scan_devices()

                self.root.after(0, done)

        threading.Thread(target=work, daemon=True).start()

    def run(self) -> None:
        self.root.mainloop()

    def on_aggressive_changed(self) -> None:
        """Handle aggressive mode checkbox changes."""
        if self.aggressive_var.get():
            # When aggressive is checked, check all other options
            self.restart_var.set(True)
            self.toggle_var.set(True)
            self.reset_stack_var.set(True)
            self.clear_cache_var.set(True)
            self.reset_drivers_var.set(True)
        else:
            # When unchecked, uncheck all advanced options but leave basic ones
            self.reset_stack_var.set(False)
            self.clear_cache_var.set(False)
            self.reset_drivers_var.set(False)

    def run_troubleshoot(self) -> None:
        """Run troubleshooting in a separate thread."""
        def work():
            try:
                self.status_var.set("Running troubleshooting...")
                self.progress.configure(mode="indeterminate")
                self.progress.start()

                # Run troubleshooting
                completed, full_stdout = run_powershell_script_streaming(
                    PS_TROUBLESHOOT_SCRIPT, [], on_event=None, timeout=60
                )

                if completed.returncode == 0:
                    self.status_var.set("Troubleshooting completed successfully")
                    # Show results in a popup
                    self.root.after(0, lambda: self.show_troubleshoot_results(full_stdout))
                else:
                    self.status_var.set("Troubleshooting failed")

            except Exception as e:
                self.status_var.set(f"Troubleshooting error: {str(e)}")
            finally:
                self.progress.stop()
                self.progress.configure(mode="determinate")
                self.progress_var.set(0)

        threading.Thread(target=work, daemon=True).start()

    def show_troubleshoot_results(self, output: str) -> None:
        """Show troubleshooting results in a popup window."""
        popup = tk.Toplevel(self.root)
        popup.title("Troubleshooting Results")
        popup.geometry("600x400")

        # Create text widget with scrollbar
        frame = ttk.Frame(popup)
        frame.pack(fill="both", expand=True, padx=10, pady=10)

        text_widget = tk.Text(frame, wrap="word")
        scrollbar = ttk.Scrollbar(frame, orient="vertical", command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        # Parse and format the output
        lines = output.splitlines()
        formatted_text = "Bluetooth Troubleshooting Results\n" + "=" * 50 + "\n\n"

        for line in lines:
            if line.startswith("##EVT## "):
                # Parse event line
                try:
                    evt_data = line[8:]  # Remove "##EVT## "
                    evt = json.loads(evt_data)
                    step = evt.get('step', '')
                    message = evt.get('message', '')
                    level = evt.get('level', 'info')

                    if level == 'error':
                        formatted_text += f"❌ [{step}] {message}\n"
                    elif level == 'success':
                        formatted_text += f"✅ [{step}] {message}\n"
                    else:
                        formatted_text += f"ℹ️  [{step}] {message}\n"
                except:
                    formatted_text += line + "\n"
            else:
                formatted_text += line + "\n"

        text_widget.insert("1.0", formatted_text)
        text_widget.configure(state="disabled")

        text_widget.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Add close button
        ttk.Button(popup, text="Close", command=popup.destroy).pack(pady=5)


def configure_logging(verbose: bool = False) -> str:
    logger = logging.getLogger("bt_repair")
    logger.setLevel(logging.DEBUG)
    log_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'bluetooth_repair.log')

    if not any(isinstance(h, logging.handlers.RotatingFileHandler) for h in logger.handlers):
        handler = logging.handlers.RotatingFileHandler(log_path, maxBytes=512_000, backupCount=3, encoding='utf-8')
        handler.setFormatter(logging.Formatter('%(asctime)s %(levelname)s %(message)s'))
        logger.addHandler(handler)

    if verbose and not any(isinstance(h, logging.StreamHandler) and h.stream == sys.stderr for h in logger.handlers):
        ch = logging.StreamHandler()
        ch.setLevel(logging.INFO)
        ch.setFormatter(logging.Formatter('%(levelname)s %(message)s'))
        logger.addHandler(ch)

    return log_path


def main() -> int:
    if not is_windows():
        print("Error: This script requires Windows.")
        return 1

    parser = argparse.ArgumentParser(
        description=("Repair a Bluetooth device on Windows by unpairing and re-pairing it using WinRT APIs.")
    )
    parser.add_argument("device", nargs="?", help="Device name (wildcards allowed, e.g. 'Hesh*' or '*Evo*')")
    parser.add_argument("--restart-service", action="store_true", help="Restart the Bluetooth service before attempting repair (requires admin)")
    parser.add_argument("--toggle-adapter", action="store_true", help="Disable/enable the Bluetooth adapter before attempting repair (requires admin)")
    parser.add_argument("--deep-clean", action="store_true", help="Perform deep cleanup: clear cache, reset stack, restart services (requires admin)")
    parser.add_argument("--reset-stack", action="store_true", help="Reset entire Bluetooth stack including drivers and registry (requires admin)")
    parser.add_argument("--clear-cache", action="store_true", help="Clear all Bluetooth cache and temporary files (requires admin)")
    parser.add_argument("--reset-drivers", action="store_true", help="Reset Bluetooth drivers and reinitialize adapters (requires admin)")
    parser.add_argument("--aggressive", action="store_true", help="Use all available repair methods (equivalent to --deep-clean --reset-stack --clear-cache --reset-drivers)")
    parser.add_argument("--gui", action="store_true", help="Open the GUI. Default when no device argument is provided.")
    parser.add_argument("--list", action="store_true", help="List available Bluetooth devices as JSON.")
    parser.add_argument("--troubleshoot", action="store_true", help="Run comprehensive Bluetooth troubleshooting and diagnostics")
    parser.add_argument("--timeout", type=int, default=60, help="Timeout in seconds for pairing/repair (default: 60)")
    parser.add_argument("--verbose", action="store_true", help="Also log to console (stderr)")

    args = parser.parse_args()

    log_path = configure_logging(verbose=args.verbose)
    logger = logging.getLogger("bt_repair")
    logger.info("Starting bt_repair (admin=%s) using PS=%s", is_user_admin(), _powershell_exe())

    needs_admin = (args.restart_service or args.toggle_adapter or args.deep_clean or
                   args.reset_stack or args.clear_cache or args.reset_drivers or args.aggressive)
    if relaunch_as_admin_if_needed(needs_admin):
        return 0

    if args.troubleshoot:
        return run_troubleshooting()

    if args.list:
        devices = list_bluetooth_devices()
        print(json.dumps(devices, indent=2))
        return 0

    if args.gui or not args.device:
        if tk is None:
            print("Error: Tkinter is not available in this Python environment.")
            return 1
        app = RepairApp()
        app.run()
        return 0

    if (args.restart_service or args.toggle_adapter) and not is_user_admin():
        print("Warning: --restart-service/--toggle-adapter requested but not running as admin. Skipping these steps.")

    # Handle aggressive mode (all repair methods)
    if args.aggressive:
        args.deep_clean = True
        args.reset_stack = True
        args.clear_cache = True
        args.reset_drivers = True
        args.restart_service = True
        args.toggle_adapter = True
        print("🔧 AGGRESSIVE MODE: Using all available repair methods...")

    # Handle advanced repair options
    repair_operations = []

    if args.clear_cache:
        repair_operations.append(("Clearing Bluetooth cache", clear_bluetooth_cache))

    if args.reset_drivers:
        repair_operations.append(("Resetting Bluetooth drivers", reset_bluetooth_drivers))

    if args.reset_stack:
        repair_operations.append(("Resetting Bluetooth stack", reset_bluetooth_stack))

    if args.deep_clean and not args.clear_cache:  # Avoid duplicate if clear_cache already added
        repair_operations.append(("Deep cleaning Bluetooth", deep_clean_bluetooth))

    # Execute repair operations
    for operation_name, operation_func in repair_operations:
        print(f"🔧 {operation_name}...")
        result = operation_func()
        if result.get("ok"):
            print(f"✅ {operation_name} completed successfully")
        else:
            print(f"❌ {operation_name} failed: {result.get('message', 'Unknown error')}")
            if not args.aggressive:  # In aggressive mode, continue even if one fails
                return 1

    try:
        result = repair_bluetooth_device(
            device_name=args.device,
            restart_service=args.restart_service,
            toggle_adapter=args.toggle_adapter,
            timeout_seconds=args.timeout,
            reset_stack=args.reset_stack,
            clear_cache=args.clear_cache,
            reset_drivers=args.reset_drivers,
        )
    except Exception as exc:
        print(f"Error: {exc}")
        logger.error("Main exception: %s\n%s", exc, traceback.format_exc())
        return 3

    ok = bool(result.get("ok"))
    name = result.get("name") or args.device
    status = result.get("resultStatus")
    if ok:
        print(f"Success: '{name}' is paired. Status={status}; Connected={result.get('isConnected')}.")
        return 0
    else:
        msg = result.get("message") or f"Pairing failed with status {status}."
        print(f"Failed to repair '{name}': {msg}")

        # If device not found, show available devices to help user
        if "not found" in msg.lower():
            try:
                devices = list_bluetooth_devices(timeout_seconds=10)
                paired_devices = [d for d in devices if d.get("isPaired") and not d.get("name", "").endswith("Transport")]
                if paired_devices:
                    print("\nAvailable paired devices:")
                    for device in paired_devices[:10]:  # Show up to 10 devices
                        print(f"  - {device.get('name', 'Unknown')}")
                    if len(paired_devices) > 10:
                        print(f"  ... and {len(paired_devices) - 10} more")
            except Exception:
                pass  # Don't fail if we can't list devices

        return 2


if __name__ == "__main__":
    sys.exit(main())