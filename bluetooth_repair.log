2025-08-14 15:51:41,870 INFO Starting bt_repair (admin=False) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 15:51:42,159 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpqzcwe7ir.ps1 -TimeoutSeconds 30
2025-08-14 15:51:44,226 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"慊牢⁡癅汯敶′㔸�","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"效桳䔠潶�","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"潂敳䴠湩⁩䥉匠⁅潓湵䱤湩k","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"瑓慥瑬⁨牐⁯托硯�","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌匠䵋夶㜨㨲䐲)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌䠠卂堭㝌�","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null}]
STDERR=param : The term 'param' is not recognized as the name of a cmdlet, function, script file, or operable program. Check the spelling of the name, or if a path was included, verify that the path is correct and 
try again.
At C:\Users\<USER>\AppData\Local\Temp\tmpqzcwe7ir.ps1:154 char:1
+ param([int]$TimeoutSeconds = 30)
+ ~~~~~
    + CategoryInfo          : ObjectNotFound: (param:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException
 
##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 18","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 15:51:44,237 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"慊牢⁡癅汯敶′㔸�","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"效桳䔠潶�","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"潂敳䴠湩⁩䥉匠⁅潓湵䱤湩k","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"瑓慥瑬⁨牐⁯托硯�","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌匠䵋夶㜨㨲䐲)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌䠠卂堭㝌�","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null}]

2025-08-14 15:51:51,181 INFO Starting bt_repair (admin=True) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 15:51:51,467 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmps6h6ie4y.ps1 -TimeoutSeconds 30
2025-08-14 15:51:53,390 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"慊牢⁡癅汯敶′㔸�","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"效桳䔠潶�","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"潂敳䴠湩⁩䥉匠⁅潓湵䱤湩k","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"瑓慥瑬⁨牐⁯托硯�","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌匠䵋夶㜨㨲䐲)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌䠠卂堭㝌�","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null}]
STDERR=param : The term 'param' is not recognized as the name of a cmdlet, function, script file, or operable program. Check 
the spelling of the name, or if a path was included, verify that the path is correct and try again.
At C:\Users\<USER>\AppData\Local\Temp\tmps6h6ie4y.ps1:154 char:1
+ param([int]$TimeoutSeconds = 30)
+ ~~~~~
    + CategoryInfo          : ObjectNotFound: (param:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException
 
##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 18","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 15:51:53,391 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"慊牢⁡癅汯敶′㔸�","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"效桳䔠潶�","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"潂敳䴠湩⁩䥉匠⁅潓湵䱤湩k","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"瑓慥瑬⁨牐⁯托硯�","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌匠䵋夶㜨㨲䐲)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌䠠卂堭㝌�","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null}]

2025-08-14 15:51:58,709 DEBUG (streaming) Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpbhrozjqr.ps1 -DeviceName "Hesh Evo" -TimeoutSeconds 60 -RestartService -ToggleAdapter
2025-08-14 15:53:13,732 DEBUG (streaming) PS rc=-9; stderr=
2025-08-14 15:54:22,201 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmporogohej.ps1 -TimeoutSeconds 30
2025-08-14 15:54:24,147 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"慊牢⁡癅汯敶′㔸�","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"效桳䔠潶�","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"潂敳䴠湩⁩䥉匠⁅潓湵䱤湩k","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"瑓慥瑬⁨牐⁯托硯�","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌匠䵋夶㜨㨲䐲)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌䠠卂堭㝌�","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null}]
STDERR=param : The term 'param' is not recognized as the name of a cmdlet, function, script file, or operable program. Check 
the spelling of the name, or if a path was included, verify that the path is correct and try again.
At C:\Users\<USER>\AppData\Local\Temp\tmporogohej.ps1:154 char:1
+ param([int]$TimeoutSeconds = 30)
+ ~~~~~
    + CategoryInfo          : ObjectNotFound: (param:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException
 
##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 18","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 15:54:24,148 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"慊牢⁡癅汯敶′㔸�","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"效桳䔠潶�","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"潂敳䴠湩⁩䥉匠⁅潓湵䱤湩k","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"瑓慥瑬⁨牐⁯托硯�","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌匠䵋夶㜨㨲䐲)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌䠠卂堭㝌�","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null}]

2025-08-14 16:02:33,316 INFO Starting bt_repair (admin=False) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:02:33,317 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpnsfuo40o.ps1
2025-08-14 16:02:34,068 DEBUG PS returncode=0
STDOUT=5
STDERR=
2025-08-14 16:02:34,070 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpf7xdr4rm.ps1
2025-08-14 16:02:35,118 DEBUG PS returncode=0
STDOUT=OK
STDERR=
2025-08-14 16:02:35,383 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmp70xo_xc4.ps1 -TimeoutSeconds 30
2025-08-14 16:02:37,490 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"慊牢⁡癅汯敶′㔸�","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"效桳䔠潶�","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"潂敳䴠湩⁩䥉匠⁅潓湵䱤湩k","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"瑓慥瑬⁨牐⁯托硯�","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌匠䵋夶㜨㨲䐲)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌䠠卂堭㝌�","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null}]
STDERR=param : The term 'param' is not recognized as the name of a cmdlet, function, script file, or operable program. Check the spelling of the name, or if a path was included, verify that the path is correct and 
try again.
At C:\Users\<USER>\AppData\Local\Temp\tmp70xo_xc4.ps1:154 char:1
+ param([int]$TimeoutSeconds = 30)
+ ~~~~~
    + CategoryInfo          : ObjectNotFound: (param:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException
 
##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 6","level":"info"}
##EVT## {"step":"list","message":"Enum registry: 0","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:02:37,490 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"慊牢⁡癅汯敶′㔸�","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"效桳䔠潶�","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"潂敳䴠湩⁩䥉匠⁅潓湵䱤湩k","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"瑓慥瑬⁨牐⁯托硯�","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌匠䵋夶㜨㨲䐲)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌䠠卂堭㝌�","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null}]

2025-08-14 16:02:40,156 INFO Starting bt_repair (admin=False) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:02:40,158 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpnt7m0xc8.ps1
2025-08-14 16:02:40,919 DEBUG PS returncode=0
STDOUT=5
STDERR=
2025-08-14 16:02:40,921 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpqfpg6rwj.ps1
2025-08-14 16:02:41,908 DEBUG PS returncode=0
STDOUT=OK
STDERR=
2025-08-14 16:02:42,181 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmptr53pges.ps1 -TimeoutSeconds 30
2025-08-14 16:02:44,174 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"慊牢⁡癅汯敶′㔸�","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"效桳䔠潶�","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"潂敳䴠湩⁩䥉匠⁅潓湵䱤湩k","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"瑓慥瑬⁨牐⁯托硯�","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌匠䵋夶㜨㨲䐲)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌䠠卂堭㝌�","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null}]
STDERR=param : The term 'param' is not recognized as the name of a cmdlet, function, script file, or operable program. Check the spelling of the name, or if a path was included, verify that the path is correct and 
try again.
At C:\Users\<USER>\AppData\Local\Temp\tmptr53pges.ps1:154 char:1
+ param([int]$TimeoutSeconds = 30)
+ ~~~~~
    + CategoryInfo          : ObjectNotFound: (param:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException
 
##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 6","level":"info"}
##EVT## {"step":"list","message":"Enum registry: 0","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:02:44,175 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"慊牢⁡癅汯敶′㔸�","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"效桳䔠潶�","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"潂敳䴠湩⁩䥉匠⁅潓湵䱤湩k","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"瑓慥瑬⁨牐⁯托硯�","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌匠䵋夶㜨㨲䐲)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌䠠卂堭㝌�","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null}]

2025-08-14 16:03:15,573 INFO Starting bt_repair (admin=True) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:03:15,574 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmp_ow45w6l.ps1
2025-08-14 16:03:16,339 DEBUG PS returncode=0
STDOUT=5
STDERR=
2025-08-14 16:03:16,340 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmp6m3ymdlk.ps1
2025-08-14 16:03:17,309 DEBUG PS returncode=0
STDOUT=OK
STDERR=
2025-08-14 16:03:17,580 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpvraall_8.ps1 -TimeoutSeconds 30
2025-08-14 16:03:19,415 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"慊牢⁡癅汯敶′㔸�","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"效桳䔠潶�","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"潂敳䴠湩⁩䥉匠⁅潓湵䱤湩k","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"瑓慥瑬⁨牐⁯托硯�","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌匠䵋夶㜨㨲䐲)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌䠠卂堭㝌�","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null}]
STDERR=param : The term 'param' is not recognized as the name of a cmdlet, function, script file, or operable program. Check 
the spelling of the name, or if a path was included, verify that the path is correct and try again.
At C:\Users\<USER>\AppData\Local\Temp\tmpvraall_8.ps1:154 char:1
+ param([int]$TimeoutSeconds = 30)
+ ~~~~~
    + CategoryInfo          : ObjectNotFound: (param:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException
 
##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 6","level":"info"}
##EVT## {"step":"list","message":"Enum registry: 0","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:03:19,416 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"慊牢⁡癅汯敶′㔸�","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"效桳䔠潶�","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"潂敳䴠湩⁩䥉匠⁅潓湵䱤湩k","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"瑓慥瑬⁨牐⁯托硯�","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌匠䵋夶㜨㨲䐲)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌䠠卂堭㝌�","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null}]

2025-08-14 16:03:24,513 DEBUG (streaming) Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpu8nu4fdq.ps1 -DeviceName "Hesh Evo" -TimeoutSeconds 60 -RestartService -ToggleAdapter
2025-08-14 16:03:25,551 DEBUG (streaming) PS rc=0; stderr=
2025-08-14 16:03:28,025 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpkd14rhe0.ps1 -TimeoutSeconds 30
2025-08-14 16:03:29,795 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"慊牢⁡癅汯敶′㔸�","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"效桳䔠潶�","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"潂敳䴠湩⁩䥉匠⁅潓湵䱤湩k","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"瑓慥瑬⁨牐⁯托硯�","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌匠䵋夶㜨㨲䐲)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌䠠卂堭㝌�","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null}]
STDERR=param : The term 'param' is not recognized as the name of a cmdlet, function, script file, or operable program. Check 
the spelling of the name, or if a path was included, verify that the path is correct and try again.
At C:\Users\<USER>\AppData\Local\Temp\tmpkd14rhe0.ps1:154 char:1
+ param([int]$TimeoutSeconds = 30)
+ ~~~~~
    + CategoryInfo          : ObjectNotFound: (param:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException
 
##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 6","level":"info"}
##EVT## {"step":"list","message":"Enum registry: 0","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:03:29,796 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"慊牢⁡癅汯敶′㔸�","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"效桳䔠潶�","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"潂敳䴠湩⁩䥉匠⁅潓湵䱤湩k","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"瑓慥瑬⁨牐⁯托硯�","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌匠䵋夶㜨㨲䐲)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌䠠卂堭㝌�","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null}]

2025-08-14 16:13:08,597 INFO Starting bt_repair (admin=False) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:13:08,599 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpi3004nug.ps1 -TimeoutSeconds 30
2025-08-14 16:13:11,428 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"慊牢⁡癅汯敶′㔸�","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"效桳䔠潶�","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"潂敳䴠湩⁩䥉匠⁅潓湵䱤湩k","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"瑓慥瑬⁨牐⁯托硯�","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌匠䵋夶㜨㨲䐲)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌䠠卂堭㝌�","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null}]
STDERR=##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 18","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:13:11,430 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"慊牢⁡癅汯敶′㔸�","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"效桳䔠潶�","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"潂敳䴠湩⁩䥉匠⁅潓湵䱤湩k","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"瑓慥瑬⁨牐⁯托硯�","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌匠䵋夶㜨㨲䐲)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌䠠卂堭㝌�","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null}]

2025-08-14 16:13:59,770 INFO Starting bt_repair (admin=False) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:13:59,771 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpj5ca6h3v.ps1 -TimeoutSeconds 30
2025-08-14 16:14:02,056 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"慊牢⁡癅汯敶′㔸�","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"效桳䔠潶�","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"潂敳䴠湩⁩䥉匠⁅潓湵䱤湩k","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"瑓慥瑬⁨牐⁯托硯�","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌匠䵋夶㜨㨲䐲)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌䠠卂堭㝌�","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null}]
STDERR=##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 18","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:14:02,057 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"慊牢⁡癅汯敶′㔸�","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"效桳䔠潶�","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"潂敳䴠湩⁩䥉匠⁅潓湵䱤湩k","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"瑓慥瑬⁨牐⁯托硯�","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌匠䵋夶㜨㨲䐲)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌䠠卂堭㝌�","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null}]

2025-08-14 16:14:47,223 INFO Starting bt_repair (admin=False) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:14:47,224 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpy7iu55l4.ps1 -TimeoutSeconds 30
2025-08-14 16:14:48,842 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null}]
STDERR=##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 18","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:14:48,843 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"i
2025-08-14 16:14:57,726 INFO Starting bt_repair (admin=False) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:14:58,007 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmp4p2lb34i.ps1 -TimeoutSeconds 30
2025-08-14 16:14:59,690 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null}]
STDERR=##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 18","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:14:59,691 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"i
2025-08-14 16:15:11,306 INFO Starting bt_repair (admin=False) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:15:11,307 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmp66adtsai.ps1 -DeviceName "Hesh Evo" -TimeoutSeconds 10
2025-08-14 16:15:23,388 DEBUG PS returncode=1
STDOUT=##EVT## {"step":"init","message":"Starting repair for \u0027Hesh Evo\u0027. Options: RestartService=False, ToggleAdapter=False","level":"info"}
##EVT## {"step":"reset","message":"No Bluetooth stack changes requested.","level":"info"}
##EVT## {"step":"discover","message":"Scanning available devices...","level":"info"}
##EVT## {"step":"discover","message":"Not found yet; waiting up to 10s for device to appear...","level":"info"}
##EVT## {"step":"discover","message":"Device not found.","level":"error"}
{"ok":false,"step":"discover","message":"Device not found. Ensure it is in pairing mode and within range.","resetPerformed":false}
STDERR=
2025-08-14 16:15:23,389 INFO repair_bluetooth_device stdout=##EVT## {"step":"init","message":"Starting repair for \u0027Hesh Evo\u0027. Options: RestartService=False, ToggleAdapter=False","level":"info"}
##EVT## {"step":"reset","message":"No Bluetooth stack changes requested.","level":"info"}
##EVT## {"step":"discover","message":"Scanning available devices...","level":"info"}
##EVT## {"step":"discover","message":"Not found yet; waiting up to 10s for device to appear...","level":"info"}
##EVT## {"step":"discover","message":"Device not found.","level":"error"}
{"ok":false,"step":"discover","message":"Device not found. Ensure it is in pairing mode and within range.","resetPerformed":false}
2025-08-14 16:15:23,389 ERROR JSON decode error: Expecting value: line 1 column 1 (char 0)
Raw stdout: ##EVT## {"step":"init","message":"Starting repair for \u0027Hesh Evo\u0027. Options: RestartService=False, ToggleAdapter=False","level":"info"}
##EVT## {"step":"reset","message":"No Bluetooth stack changes requested.","level":"info"}
##EVT## {"step":"discover","message":"Scanning available devices...","level":"info"}
##EVT## {"step":"discover","message":"Not found yet; waiting up to 10s for device to appear...","level":"info"}
##EVT## {"step":"discover","message":"Device not found.","level":"error"}
{"ok":false,"step":"discover","message":"Device not found. Ensure it is in pairing mode and within range.","resetPerformed":false}
2025-08-14 16:15:55,111 INFO Starting bt_repair (admin=False) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:15:55,113 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmp_pl1qij1.ps1 -DeviceName "Hesh Evo" -TimeoutSeconds 10
2025-08-14 16:16:07,375 DEBUG PS returncode=1
STDOUT=##EVT## {"step":"init","message":"Starting repair for \u0027Hesh Evo\u0027. Options: RestartService=False, ToggleAdapter=False","level":"info"}
##EVT## {"step":"reset","message":"No Bluetooth stack changes requested.","level":"info"}
##EVT## {"step":"discover","message":"Scanning available devices...","level":"info"}
##EVT## {"step":"discover","message":"Not found yet; waiting up to 10s for device to appear...","level":"info"}
##EVT## {"step":"discover","message":"Device not found.","level":"error"}
{"ok":false,"step":"discover","message":"Device not found. Ensure it is in pairing mode and within range.","resetPerformed":false}
STDERR=
2025-08-14 16:16:07,376 INFO repair_bluetooth_device stdout=##EVT## {"step":"init","message":"Starting repair for \u0027Hesh Evo\u0027. Options: RestartService=False, ToggleAdapter=False","level":"info"}
##EVT## {"step":"reset","message":"No Bluetooth stack changes requested.","level":"info"}
##EVT## {"step":"discover","message":"Scanning available devices...","level":"info"}
##EVT## {"step":"discover","message":"Not found yet; waiting up to 10s for device to appear...","level":"info"}
##EVT## {"step":"discover","message":"Device not found.","level":"error"}
{"ok":false,"step":"discover","message":"Device not found. Ensure it is in pairing mode and within range.","resetPerformed":false}
2025-08-14 16:16:30,683 INFO Starting bt_repair (admin=False) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:16:30,685 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmp3dkpt0oy.ps1 -DeviceName NonExistentDevice -TimeoutSeconds 5
2025-08-14 16:16:37,658 DEBUG PS returncode=1
STDOUT=##EVT## {"step":"init","message":"Starting repair for \u0027NonExistentDevice\u0027. Options: RestartService=False, ToggleAdapter=False","level":"info"}
##EVT## {"step":"reset","message":"No Bluetooth stack changes requested.","level":"info"}
##EVT## {"step":"discover","message":"Scanning available devices...","level":"info"}
##EVT## {"step":"discover","message":"Not found yet; waiting up to 5s for device to appear...","level":"info"}
##EVT## {"step":"discover","message":"Device not found.","level":"error"}
{"ok":false,"step":"discover","message":"Device not found. Ensure it is in pairing mode and within range.","resetPerformed":false}
STDERR=
2025-08-14 16:16:37,659 INFO repair_bluetooth_device stdout=##EVT## {"step":"init","message":"Starting repair for \u0027NonExistentDevice\u0027. Options: RestartService=False, ToggleAdapter=False","level":"info"}
##EVT## {"step":"reset","message":"No Bluetooth stack changes requested.","level":"info"}
##EVT## {"step":"discover","message":"Scanning available devices...","level":"info"}
##EVT## {"step":"discover","message":"Not found yet; waiting up to 5s for device to appear...","level":"info"}
##EVT## {"step":"discover","message":"Device not found.","level":"error"}
{"ok":false,"step":"discover","message":"Device not found. Ensure it is in pairing mode and within range.","resetPerformed":false}
2025-08-14 16:16:37,661 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpktqah2ae.ps1 -TimeoutSeconds 10
2025-08-14 16:16:39,607 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null}]
STDERR=##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 18","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:16:39,608 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"i
2025-08-14 16:17:14,235 INFO Starting bt_repair (admin=False) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:17:14,236 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpe2j4ztat.ps1 -TimeoutSeconds 30
2025-08-14 16:17:16,042 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null}]
STDERR=##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 18","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:17:16,043 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"i
2025-08-14 16:17:16,277 INFO Starting bt_repair (admin=False) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:17:16,279 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpd7llgtnz.ps1 -DeviceName 'NonExistentTestDevice12345' -TimeoutSeconds 5
2025-08-14 16:17:23,475 DEBUG PS returncode=1
STDOUT=##EVT## {"step":"init","message":"Starting repair for \u0027\u0027NonExistentTestDevice12345\u0027\u0027. Options: RestartService=False, ToggleAdapter=False","level":"info"}
##EVT## {"step":"reset","message":"No Bluetooth stack changes requested.","level":"info"}
##EVT## {"step":"discover","message":"Scanning available devices...","level":"info"}
##EVT## {"step":"discover","message":"Not found yet; waiting up to 5s for device to appear...","level":"info"}
##EVT## {"step":"discover","message":"Device not found.","level":"error"}
{"ok":false,"step":"discover","message":"Device not found. Ensure it is in pairing mode and within range.","resetPerformed":false}
STDERR=
2025-08-14 16:17:23,475 INFO repair_bluetooth_device stdout=##EVT## {"step":"init","message":"Starting repair for \u0027\u0027NonExistentTestDevice12345\u0027\u0027. Options: RestartService=False, ToggleAdapter=False","level":"info"}
##EVT## {"step":"reset","message":"No Bluetooth stack changes requested.","level":"info"}
##EVT## {"step":"discover","message":"Scanning available devices...","level":"info"}
##EVT## {"step":"discover","message":"Not found yet; waiting up to 5s for device to appear...","level":"info"}
##EVT## {"step":"discover","message":"Device not found.","level":"error"}
{"ok":false,"step":"discover","message":"Device not found. Ensure it is in pairing mode and within range.","resetPerformed":false}
2025-08-14 16:17:23,477 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmp1ome4pif.ps1 -TimeoutSeconds 10
2025-08-14 16:17:25,454 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null}]
STDERR=##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 18","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:17:25,454 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"i
2025-08-14 16:18:19,125 INFO Starting bt_repair (admin=False) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:18:19,397 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmp2p557tj4.ps1 -TimeoutSeconds 30
2025-08-14 16:18:21,018 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null}]
STDERR=##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 18","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:18:21,019 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"i
2025-08-14 16:18:30,659 INFO Starting bt_repair (admin=True) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:18:30,937 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpkvo6d555.ps1 -TimeoutSeconds 30
2025-08-14 16:18:32,618 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null}]
STDERR=##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 18","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:18:32,619 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"i
2025-08-14 16:18:40,469 DEBUG (streaming) Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpi6v0_cyx.ps1 -DeviceName "Hesh Evo" -TimeoutSeconds 60 -RestartService -ToggleAdapter
2025-08-14 16:19:55,493 DEBUG (streaming) PS rc=-9; stderr=
2025-08-14 16:20:11,964 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpmgrttos1.ps1 -TimeoutSeconds 30
2025-08-14 16:20:13,608 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null}]
STDERR=##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 18","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:20:13,610 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"i
2025-08-14 16:21:21,563 INFO Starting bt_repair (admin=False) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:21:21,565 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmp9rk613_c.ps1 -DeviceName NonExistentDevice -TimeoutSeconds 5
2025-08-14 16:21:28,641 DEBUG PS returncode=1
STDOUT=##EVT## {"step":"init","message":"Starting repair for \u0027NonExistentDevice\u0027. Options: RestartService=False, ToggleAdapter=False","level":"info"}
##EVT## {"step":"reset","message":"No Bluetooth stack changes requested.","level":"info"}
##EVT## {"step":"discover","message":"Scanning available devices...","level":"info"}
##EVT## {"step":"discover","message":"Not found yet; waiting up to 5s for device to appear...","level":"info"}
##EVT## {"step":"discover","message":"Device not found.","level":"error"}
{"ok":false,"step":"discover","message":"Device not found. Ensure it is in pairing mode and within range.","resetPerformed":false}
STDERR=
2025-08-14 16:21:28,642 INFO repair_bluetooth_device stdout=##EVT## {"step":"init","message":"Starting repair for \u0027NonExistentDevice\u0027. Options: RestartService=False, ToggleAdapter=False","level":"info"}
##EVT## {"step":"reset","message":"No Bluetooth stack changes requested.","level":"info"}
##EVT## {"step":"discover","message":"Scanning available devices...","level":"info"}
##EVT## {"step":"discover","message":"Not found yet; waiting up to 5s for device to appear...","level":"info"}
##EVT## {"step":"discover","message":"Device not found.","level":"error"}
{"ok":false,"step":"discover","message":"Device not found. Ensure it is in pairing mode and within range.","resetPerformed":false}
2025-08-14 16:21:28,644 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpy7lyliif.ps1 -TimeoutSeconds 10
2025-08-14 16:21:30,572 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null}]
STDERR=##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 18","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:21:30,573 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"i
2025-08-14 16:21:53,430 INFO Starting bt_repair (admin=False) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:21:53,432 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmp20ij3xrc.ps1 -DeviceName NonExistentTestDevice12345 -TimeoutSeconds 3
2025-08-14 16:21:58,014 DEBUG PS returncode=1
STDOUT=##EVT## {"step":"init","message":"Starting repair for \u0027NonExistentTestDevice12345\u0027. Options: RestartService=False, ToggleAdapter=False","level":"info"}
##EVT## {"step":"reset","message":"No Bluetooth stack changes requested.","level":"info"}
##EVT## {"step":"discover","message":"Scanning available devices...","level":"info"}
##EVT## {"step":"discover","message":"Not found yet; waiting up to 3s for device to appear...","level":"info"}
##EVT## {"step":"discover","message":"Device not found.","level":"error"}
{"ok":false,"step":"discover","message":"Device not found. Ensure it is in pairing mode and within range.","resetPerformed":false}
STDERR=
2025-08-14 16:21:58,015 INFO repair_bluetooth_device stdout=##EVT## {"step":"init","message":"Starting repair for \u0027NonExistentTestDevice12345\u0027. Options: RestartService=False, ToggleAdapter=False","level":"info"}
##EVT## {"step":"reset","message":"No Bluetooth stack changes requested.","level":"info"}
##EVT## {"step":"discover","message":"Scanning available devices...","level":"info"}
##EVT## {"step":"discover","message":"Not found yet; waiting up to 3s for device to appear...","level":"info"}
##EVT## {"step":"discover","message":"Device not found.","level":"error"}
{"ok":false,"step":"discover","message":"Device not found. Ensure it is in pairing mode and within range.","resetPerformed":false}
2025-08-14 16:21:58,016 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpmnhjdemi.ps1 -TimeoutSeconds 10
2025-08-14 16:21:59,937 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null}]
STDERR=##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 18","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:21:59,938 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"i
2025-08-14 16:23:11,579 INFO Starting bt_repair (admin=False) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:23:11,848 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpuxbxwq79.ps1 -TimeoutSeconds 30
2025-08-14 16:23:13,480 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null}]
STDERR=##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 18","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:23:13,482 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"i
2025-08-14 16:23:19,421 INFO Starting bt_repair (admin=True) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:23:19,691 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmp5guh07dy.ps1 -TimeoutSeconds 30
2025-08-14 16:23:21,354 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null}]
STDERR=##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 18","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:23:21,355 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"i
2025-08-14 16:23:29,203 DEBUG (streaming) Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpv8_6gsv4.ps1 -DeviceName "Hesh Evo" -TimeoutSeconds 60 -RestartService -ToggleAdapter
2025-08-14 16:24:44,221 DEBUG (streaming) PS rc=-9; stderr=
2025-08-14 16:26:13,020 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpf9hhub5t.ps1 -TimeoutSeconds 30
2025-08-14 16:26:14,918 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null}]
STDERR=##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 18","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:26:14,919 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"i
2025-08-14 16:29:20,198 INFO Starting bt_repair (admin=False) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:29:20,200 DEBUG (streaming) Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpfbdandnt.ps1
2025-08-14 16:29:21,902 DEBUG (streaming) PS rc=0; stderr=
2025-08-14 16:29:29,643 INFO Starting bt_repair (admin=False) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:31:32,097 ERROR Deep clean failed: PowerShell param block must be the first non-comment statement
2025-08-14 16:32:02,966 INFO Starting bt_repair (admin=False) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:32:02,969 DEBUG (streaming) Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpho5co7yi.ps1
2025-08-14 16:32:04,597 DEBUG (streaming) PS rc=0; stderr=
2025-08-14 16:34:24,887 INFO Starting bt_repair (admin=False) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:34:25,154 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpq7yd4xr8.ps1 -TimeoutSeconds 30
2025-08-14 16:34:27,065 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null}]
STDERR=##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 18","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:34:27,066 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"i
2025-08-14 16:34:30,546 INFO Starting bt_repair (admin=True) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:34:30,815 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmp00hldyzc.ps1 -TimeoutSeconds 30
2025-08-14 16:34:32,421 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null}]
STDERR=##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 18","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:34:32,422 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"i
2025-08-14 16:34:39,014 DEBUG (streaming) Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpawhzp6wv.ps1 -DeviceName "Hesh Evo" -TimeoutSeconds 60 -RestartService -ToggleAdapter
2025-08-14 16:35:54,036 DEBUG (streaming) PS rc=-9; stderr=
2025-08-14 16:36:02,898 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpbc7w3ld3.ps1 -TimeoutSeconds 30
2025-08-14 16:36:04,490 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null}]
STDERR=##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 18","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:36:04,490 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"i
2025-08-14 16:39:11,548 INFO Starting bt_repair (admin=False) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:39:11,551 DEBUG (streaming) Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmp12_bc1jc.ps1
2025-08-14 16:39:12,475 DEBUG Malformed event payload: '{"step":"test","message":"Testing Bluetooth stack components...","level":"info"}'
2025-08-14 16:39:12,542 DEBUG Malformed event payload: '{"step":"test","message":"Bluetooth service status: Running","level":"info"}'
2025-08-14 16:39:13,129 DEBUG Malformed event payload: '{"step":"test","message":"Adapter: Realtek Wireless Bluetooth Adapter - Status: OK","level":"info"}'
2025-08-14 16:39:13,185 DEBUG Malformed event payload: '{"step":"test","message":"WinRT API not returning devices","level":"error"}'
2025-08-14 16:39:13,205 DEBUG Malformed event payload: '{"step":"test","message":"Registry access OK - found 8 paired devices","level":"info"}'
2025-08-14 16:39:13,206 DEBUG Malformed event payload: '{"step":"complete","message":"Troubleshooting complete","level":"info"}'
2025-08-14 16:39:13,245 DEBUG (streaming) PS rc=0; stderr=
2025-08-14 16:39:13,450 INFO Starting bt_repair (admin=False) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:39:13,452 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpzr1a3h9z.ps1 -TimeoutSeconds 30
2025-08-14 16:39:15,329 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null}]
STDERR=##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 18","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:39:15,330 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"i
2025-08-14 16:42:33,852 INFO Starting bt_repair (admin=False) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:42:34,114 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmp6odeg_zp.ps1 -TimeoutSeconds 30
2025-08-14 16:42:36,172 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null}]
STDERR=##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 18","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:42:36,173 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"i
2025-08-14 16:42:42,388 INFO Starting bt_repair (admin=True) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:42:42,668 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpq2gpto8d.ps1 -TimeoutSeconds 30
2025-08-14 16:42:44,302 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null}]
STDERR=##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 18","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:42:44,303 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"i
2025-08-14 16:42:53,545 DEBUG (streaming) Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpbe3nsr5u.ps1 -DeviceName "Hesh Evo" -TimeoutSeconds 60 -RestartService -ToggleAdapter
2025-08-14 16:44:08,563 DEBUG (streaming) PS rc=-9; stderr=
2025-08-14 16:44:28,135 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpnb7ktuus.ps1 -TimeoutSeconds 30
2025-08-14 16:44:29,831 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null}]
STDERR=##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 18","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:44:29,831 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"i
2025-08-14 16:47:39,925 INFO Starting bt_repair (admin=False) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:47:40,570 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpv9nfyv30.ps1 -TimeoutSeconds 30
2025-08-14 16:47:42,164 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null}]
STDERR=##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 18","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:47:42,166 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"i
2025-08-14 16:48:50,959 INFO Starting bt_repair (admin=False) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:48:51,567 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpp4uy4ejn.ps1 -TimeoutSeconds 30
2025-08-14 16:48:53,214 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null}]
STDERR=##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 18","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:48:53,215 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"i
2025-08-14 16:48:55,759 INFO Starting bt_repair (admin=True) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:48:56,399 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpav_a258r.ps1 -TimeoutSeconds 30
2025-08-14 16:48:58,131 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null}]
STDERR=##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 18","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:48:58,132 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"i
2025-08-14 16:49:11,302 DEBUG (streaming) Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpz4rzkn05.ps1
2025-08-14 16:49:12,911 DEBUG (streaming) PS rc=0; stderr=
2025-08-14 16:49:19,633 ERROR Deep clean failed: PowerShell param block must be the first non-comment statement
2025-08-14 16:49:19,662 ERROR Driver reset failed: PowerShell param block must be the first non-comment statement
2025-08-14 16:49:19,669 ERROR Stack reset failed: PowerShell param block must be the first non-comment statement
2025-08-14 16:49:19,673 DEBUG (streaming) Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmptlsgmu23.ps1 -DeviceName "Hesh Evo" -TimeoutSeconds 60 -RestartService -ToggleAdapter
2025-08-14 16:50:34,688 DEBUG (streaming) PS rc=-9; stderr=
2025-08-14 16:50:56,126 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmplj5h3u86.ps1 -TimeoutSeconds 30
2025-08-14 16:50:58,003 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null}]
STDERR=##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 18","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:50:58,004 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"i
2025-08-14 16:52:06,439 INFO Starting bt_repair (admin=False) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:52:09,401 INFO Starting bt_repair (admin=True) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:52:09,403 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpe7gjjhvj.ps1
2025-08-14 16:52:11,395 DEBUG PS returncode=0
STDOUT={"ok":true,"message":"Deep cleanup completed","step":"deep-clean"}
STDERR=##EVT## {"step":"clean","message":"Clearing Bluetooth cache and temporary files...","level":"info"}
##EVT## {"step":"clean","message":"Bluetooth service stopped","level":"info"}
##EVT## {"step":"clean","message":"Bluetooth service restarted","level":"info"}
##EVT## {"step":"deep-clean","message":"Performing additional cleanup...","level":"info"}
##EVT## {"step":"deep-clean","message":"Winsock reset completed","level":"info"}
2025-08-14 16:52:11,397 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpjonie4sg.ps1 -DeviceName TestDevice -TimeoutSeconds 10
2025-08-14 16:52:17,584 INFO Starting bt_repair (admin=False) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:52:17,587 DEBUG (streaming) Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpuh2jl7g4.ps1
2025-08-14 16:52:19,280 DEBUG (streaming) PS rc=0; stderr=
2025-08-14 16:52:23,345 DEBUG PS returncode=1
STDOUT=##EVT## {"step":"init","message":"Starting repair for \u0027TestDevice\u0027. Options: RestartService=False, ToggleAdapter=False","level":"info"}
##EVT## {"step":"reset","message":"No Bluetooth stack changes requested.","level":"info"}
##EVT## {"step":"discover","message":"Scanning available devices...","level":"info"}
##EVT## {"step":"discover","message":"Not found yet; waiting up to 10s for device to appear...","level":"info"}
##EVT## {"step":"discover","message":"Device not found.","level":"error"}
{"ok":false,"step":"discover","message":"Device not found. Ensure it is in pairing mode and within range.","resetPerformed":false}
STDERR=
2025-08-14 16:52:23,346 INFO repair_bluetooth_device stdout=##EVT## {"step":"init","message":"Starting repair for \u0027TestDevice\u0027. Options: RestartService=False, ToggleAdapter=False","level":"info"}
##EVT## {"step":"reset","message":"No Bluetooth stack changes requested.","level":"info"}
##EVT## {"step":"discover","message":"Scanning available devices...","level":"info"}
##EVT## {"step":"discover","message":"Not found yet; waiting up to 10s for device to appear...","level":"info"}
##EVT## {"step":"discover","message":"Device not found.","level":"error"}
{"ok":false,"step":"discover","message":"Device not found. Ensure it is in pairing mode and within range.","resetPerformed":false}
2025-08-14 16:52:23,347 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpx1gt599s.ps1 -TimeoutSeconds 10
2025-08-14 16:52:25,265 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null}]
STDERR=##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 18","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:52:25,266 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"i
2025-08-14 16:52:28,221 INFO Starting bt_repair (admin=False) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:52:28,848 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmp4gco_lis.ps1 -TimeoutSeconds 30
2025-08-14 16:52:30,516 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null}]
STDERR=##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 18","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:52:30,517 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"i
2025-08-14 16:59:10,392 INFO Starting bt_repair (admin=True) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:59:11,002 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpc3tdy3kl.ps1 -TimeoutSeconds 30
2025-08-14 16:59:12,952 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null}]
STDERR=##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 18","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:59:12,953 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"i
2025-08-14 16:59:20,649 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpw0dwg5p5.ps1
2025-08-14 16:59:22,340 DEBUG PS returncode=0
STDOUT={"ok":true,"message":"Deep cleanup completed","step":"deep-clean"}
STDERR=##EVT## {"step":"clean","message":"Clearing Bluetooth cache and temporary files...","level":"info"}
##EVT## {"step":"clean","message":"Bluetooth service stopped","level":"info"}
##EVT## {"step":"clean","message":"Bluetooth service restarted","level":"info"}
##EVT## {"step":"deep-clean","message":"Performing additional cleanup...","level":"info"}
##EVT## {"step":"deep-clean","message":"Winsock reset completed","level":"info"}
2025-08-14 16:59:22,351 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpl33e71xo.ps1
2025-08-14 16:59:30,309 DEBUG PS returncode=0
STDOUT={"ok":true,"message":"Bluetooth drivers reset completed","step":"driver-reset"}
STDERR=##EVT## {"step":"driver-reset","message":"Resetting Bluetooth drivers...","level":"info"}
##EVT## {"step":"driver-reset","message":"Disabling adapter: Realtek Wireless Bluetooth Adapter","level":"info"}
##EVT## {"step":"driver-reset","message":"Re-enabling adapter: Realtek Wireless Bluetooth Adapter","level":"info"}
##EVT## {"step":"driver-reset","message":"Bluetooth driver reset completed","level":"info"}
2025-08-14 16:59:30,311 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpa9_7s3lu.ps1
2025-08-14 17:02:30,337 ERROR Stack reset failed: Command '['C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\powershell.exe', '-sta', '-NoProfile', '-NonInteractive', '-NoLogo', '-ExecutionPolicy', 'Bypass', '-File', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpa9_7s3lu.ps1']' timed out after 180 seconds
2025-08-14 17:02:30,346 DEBUG (streaming) Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmp_yv4q98o.ps1 -DeviceName "Hesh Evo" -TimeoutSeconds 60 -RestartService -ToggleAdapter
2025-08-14 17:03:45,367 DEBUG (streaming) PS rc=-9; stderr=
2025-08-14 17:03:48,842 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpbbto0l2c.ps1 -TimeoutSeconds 30
2025-08-14 17:03:50,575 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null}]
STDERR=##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 18","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 17:03:50,576 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Device_0004328DFB0C","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_044EAF83722D","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_50C275797C56","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_60C5E6BAF10E","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_782B645D12DA","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Device_B8F8BEBEB5FA","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"i
