2025-08-14 15:51:41,870 INFO Starting bt_repair (admin=False) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 15:51:42,159 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpqzcwe7ir.ps1 -TimeoutSeconds 30
2025-08-14 15:51:44,226 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"慊牢⁡癅汯敶′㔸�","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"效桳䔠潶�","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"潂敳䴠湩⁩䥉匠⁅潓湵䱤湩k","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"瑓慥瑬⁨牐⁯托硯�","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌匠䵋夶㜨㨲䐲)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌䠠卂堭㝌�","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null}]
STDERR=param : The term 'param' is not recognized as the name of a cmdlet, function, script file, or operable program. Check the spelling of the name, or if a path was included, verify that the path is correct and 
try again.
At C:\Users\<USER>\AppData\Local\Temp\tmpqzcwe7ir.ps1:154 char:1
+ param([int]$TimeoutSeconds = 30)
+ ~~~~~
    + CategoryInfo          : ObjectNotFound: (param:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException
 
##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 18","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 15:51:44,237 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"慊牢⁡癅汯敶′㔸�","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"效桳䔠潶�","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"潂敳䴠湩⁩䥉匠⁅潓湵䱤湩k","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"瑓慥瑬⁨牐⁯托硯�","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌匠䵋夶㜨㨲䐲)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌䠠卂堭㝌�","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null}]

2025-08-14 15:51:51,181 INFO Starting bt_repair (admin=True) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 15:51:51,467 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmps6h6ie4y.ps1 -TimeoutSeconds 30
2025-08-14 15:51:53,390 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"慊牢⁡癅汯敶′㔸�","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"效桳䔠潶�","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"潂敳䴠湩⁩䥉匠⁅潓湵䱤湩k","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"瑓慥瑬⁨牐⁯托硯�","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌匠䵋夶㜨㨲䐲)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌䠠卂堭㝌�","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null}]
STDERR=param : The term 'param' is not recognized as the name of a cmdlet, function, script file, or operable program. Check 
the spelling of the name, or if a path was included, verify that the path is correct and try again.
At C:\Users\<USER>\AppData\Local\Temp\tmps6h6ie4y.ps1:154 char:1
+ param([int]$TimeoutSeconds = 30)
+ ~~~~~
    + CategoryInfo          : ObjectNotFound: (param:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException
 
##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 18","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 15:51:53,391 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"慊牢⁡癅汯敶′㔸�","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"效桳䔠潶�","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"潂敳䴠湩⁩䥉匠⁅潓湵䱤湩k","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"瑓慥瑬⁨牐⁯托硯�","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌匠䵋夶㜨㨲䐲)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌䠠卂堭㝌�","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null}]

2025-08-14 15:51:58,709 DEBUG (streaming) Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpbhrozjqr.ps1 -DeviceName "Hesh Evo" -TimeoutSeconds 60 -RestartService -ToggleAdapter
2025-08-14 15:53:13,732 DEBUG (streaming) PS rc=-9; stderr=
2025-08-14 15:54:22,201 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmporogohej.ps1 -TimeoutSeconds 30
2025-08-14 15:54:24,147 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"慊牢⁡癅汯敶′㔸�","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"效桳䔠潶�","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"潂敳䴠湩⁩䥉匠⁅潓湵䱤湩k","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"瑓慥瑬⁨牐⁯托硯�","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌匠䵋夶㜨㨲䐲)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌䠠卂堭㝌�","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null}]
STDERR=param : The term 'param' is not recognized as the name of a cmdlet, function, script file, or operable program. Check 
the spelling of the name, or if a path was included, verify that the path is correct and try again.
At C:\Users\<USER>\AppData\Local\Temp\tmporogohej.ps1:154 char:1
+ param([int]$TimeoutSeconds = 30)
+ ~~~~~
    + CategoryInfo          : ObjectNotFound: (param:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException
 
##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 18","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 15:54:24,148 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7 Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D) Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox Avrcp Transport","address":null,"isPaired":true,"isConnected":null,"signal":null},{"name":"慊牢⁡癅汯敶′㔸�","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"效桳䔠潶�","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"潂敳䴠湩⁩䥉匠⁅潓湵䱤湩k","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"瑓慥瑬⁨牐⁯托硯�","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌匠䵋夶㜨㨲䐲)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌䠠卂堭㝌�","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null}]

2025-08-14 16:02:33,316 INFO Starting bt_repair (admin=False) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:02:33,317 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpnsfuo40o.ps1
2025-08-14 16:02:34,068 DEBUG PS returncode=0
STDOUT=5
STDERR=
2025-08-14 16:02:34,070 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpf7xdr4rm.ps1
2025-08-14 16:02:35,118 DEBUG PS returncode=0
STDOUT=OK
STDERR=
2025-08-14 16:02:35,383 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmp70xo_xc4.ps1 -TimeoutSeconds 30
2025-08-14 16:02:37,490 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"慊牢⁡癅汯敶′㔸�","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"效桳䔠潶�","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"潂敳䴠湩⁩䥉匠⁅潓湵䱤湩k","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"瑓慥瑬⁨牐⁯托硯�","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌匠䵋夶㜨㨲䐲)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌䠠卂堭㝌�","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null}]
STDERR=param : The term 'param' is not recognized as the name of a cmdlet, function, script file, or operable program. Check the spelling of the name, or if a path was included, verify that the path is correct and 
try again.
At C:\Users\<USER>\AppData\Local\Temp\tmp70xo_xc4.ps1:154 char:1
+ param([int]$TimeoutSeconds = 30)
+ ~~~~~
    + CategoryInfo          : ObjectNotFound: (param:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException
 
##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 6","level":"info"}
##EVT## {"step":"list","message":"Enum registry: 0","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:02:37,490 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"慊牢⁡癅汯敶′㔸�","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"效桳䔠潶�","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"潂敳䴠湩⁩䥉匠⁅潓湵䱤湩k","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"瑓慥瑬⁨牐⁯托硯�","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌匠䵋夶㜨㨲䐲)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌䠠卂堭㝌�","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null}]

2025-08-14 16:02:40,156 INFO Starting bt_repair (admin=False) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:02:40,158 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpnt7m0xc8.ps1
2025-08-14 16:02:40,919 DEBUG PS returncode=0
STDOUT=5
STDERR=
2025-08-14 16:02:40,921 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpqfpg6rwj.ps1
2025-08-14 16:02:41,908 DEBUG PS returncode=0
STDOUT=OK
STDERR=
2025-08-14 16:02:42,181 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmptr53pges.ps1 -TimeoutSeconds 30
2025-08-14 16:02:44,174 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"慊牢⁡癅汯敶′㔸�","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"效桳䔠潶�","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"潂敳䴠湩⁩䥉匠⁅潓湵䱤湩k","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"瑓慥瑬⁨牐⁯托硯�","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌匠䵋夶㜨㨲䐲)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌䠠卂堭㝌�","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null}]
STDERR=param : The term 'param' is not recognized as the name of a cmdlet, function, script file, or operable program. Check the spelling of the name, or if a path was included, verify that the path is correct and 
try again.
At C:\Users\<USER>\AppData\Local\Temp\tmptr53pges.ps1:154 char:1
+ param([int]$TimeoutSeconds = 30)
+ ~~~~~
    + CategoryInfo          : ObjectNotFound: (param:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException
 
##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 6","level":"info"}
##EVT## {"step":"list","message":"Enum registry: 0","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:02:44,175 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"慊牢⁡癅汯敶′㔸�","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"效桳䔠潶�","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"潂敳䴠湩⁩䥉匠⁅潓湵䱤湩k","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"瑓慥瑬⁨牐⁯托硯�","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌匠䵋夶㜨㨲䐲)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌䠠卂堭㝌�","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null}]

2025-08-14 16:03:15,573 INFO Starting bt_repair (admin=True) using PS=C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe
2025-08-14 16:03:15,574 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmp_ow45w6l.ps1
2025-08-14 16:03:16,339 DEBUG PS returncode=0
STDOUT=5
STDERR=
2025-08-14 16:03:16,340 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmp6m3ymdlk.ps1
2025-08-14 16:03:17,309 DEBUG PS returncode=0
STDOUT=OK
STDERR=
2025-08-14 16:03:17,580 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpvraall_8.ps1 -TimeoutSeconds 30
2025-08-14 16:03:19,415 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"慊牢⁡癅汯敶′㔸�","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"效桳䔠潶�","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"潂敳䴠湩⁩䥉匠⁅潓湵䱤湩k","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"瑓慥瑬⁨牐⁯托硯�","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌匠䵋夶㜨㨲䐲)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌䠠卂堭㝌�","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null}]
STDERR=param : The term 'param' is not recognized as the name of a cmdlet, function, script file, or operable program. Check 
the spelling of the name, or if a path was included, verify that the path is correct and try again.
At C:\Users\<USER>\AppData\Local\Temp\tmpvraall_8.ps1:154 char:1
+ param([int]$TimeoutSeconds = 30)
+ ~~~~~
    + CategoryInfo          : ObjectNotFound: (param:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException
 
##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 6","level":"info"}
##EVT## {"step":"list","message":"Enum registry: 0","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:03:19,416 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"慊牢⁡癅汯敶′㔸�","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"效桳䔠潶�","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"潂敳䴠湩⁩䥉匠⁅潓湵䱤湩k","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"瑓慥瑬⁨牐⁯托硯�","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌匠䵋夶㜨㨲䐲)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌䠠卂堭㝌�","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null}]

2025-08-14 16:03:24,513 DEBUG (streaming) Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpu8nu4fdq.ps1 -DeviceName "Hesh Evo" -TimeoutSeconds 60 -RestartService -ToggleAdapter
2025-08-14 16:03:25,551 DEBUG (streaming) PS rc=0; stderr=
2025-08-14 16:03:28,025 DEBUG Running PowerShell: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe -sta -NoProfile -NonInteractive -NoLogo -ExecutionPolicy Bypass -File C:\Users\<USER>\AppData\Local\Temp\tmpkd14rhe0.ps1 -TimeoutSeconds 30
2025-08-14 16:03:29,795 DEBUG PS returncode=0
STDOUT=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"慊牢⁡癅汯敶′㔸�","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"效桳䔠潶�","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"潂敳䴠湩⁩䥉匠⁅潓湵䱤湩k","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"瑓慥瑬⁨牐⁯托硯�","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌匠䵋夶㜨㨲䐲)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌䠠卂堭㝌�","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null}]
STDERR=param : The term 'param' is not recognized as the name of a cmdlet, function, script file, or operable program. Check 
the spelling of the name, or if a path was included, verify that the path is correct and try again.
At C:\Users\<USER>\AppData\Local\Temp\tmpkd14rhe0.ps1:154 char:1
+ param([int]$TimeoutSeconds = 30)
+ ~~~~~
    + CategoryInfo          : ObjectNotFound: (param:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException
 
##EVT## {"step":"list","message":"AEP devices: 0","level":"info"}
##EVT## {"step":"list","message":"AEP paired: 0","level":"info"}
##EVT## {"step":"list","message":"PnP: 6","level":"info"}
##EVT## {"step":"list","message":"Enum registry: 0","level":"info"}
##EVT## {"step":"list","message":"BTHPORT registry: 8","level":"info"}
2025-08-14 16:03:29,796 INFO list_bluetooth_devices stdout=[{"name":"54b7bdbcc179","address":"54:B7:BD:BC:C1:79","isPaired":true,"isConnected":null,"signal":null},{"name":"8cc84b81a5bc","address":"8C:C8:4B:81:A5:BC","isPaired":true,"isConnected":null,"signal":null},{"name":"Bose Mini II SE SoundLink","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"Hesh Evo","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"Jabra Evolve2 85","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"LG HBS-XL7","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null},{"name":"LG SKM6Y(72:2D)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"Stealth Pro Xbox","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"慊牢⁡癅汯敶′㔸�","address":"50:C2:75:79:7C:56","isPaired":true,"isConnected":null,"signal":null},{"name":"效桳䔠潶�","address":"60:C5:E6:BA:F1:0E","isPaired":true,"isConnected":null,"signal":null},{"name":"潂敳䴠湩⁩䥉匠⁅潓湵䱤湩k","address":"78:2B:64:5D:12:DA","isPaired":true,"isConnected":null,"signal":null},{"name":"瑓慥瑬⁨牐⁯托硯�","address":"00:04:32:8D:FB:0C","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌匠䵋夶㜨㨲䐲)","address":"04:4E:AF:83:72:2D","isPaired":true,"isConnected":null,"signal":null},{"name":"䝌䠠卂堭㝌�","address":"B8:F8:BE:BE:B5:FA","isPaired":true,"isConnected":null,"signal":null}]

