# Bluetooth Troubleshooting Guide for "Hesh Evo"

## Problem Identified
Your troubleshooting revealed the root cause: **WinRT API is not returning devices**. This is a Windows Bluetooth stack issue that prevents device discovery even when devices are in pairing mode.

## Diagnostic Results
✅ Bluetooth service: Running  
✅ Bluetooth adapter: OK  
❌ **WinRT API: Not returning devices** ← This is the problem  
✅ Registry access: OK (8 paired devices found)

## Solutions to Try (In Order)

### 1. **Run as Administrator** (Most Important)
The WinRT Bluetooth APIs often require elevated privileges to work properly.

```bash
# Right-click Command Prompt → "Run as administrator", then:
python bluetooth_repair.py "Hesh Evo" --restart-service --toggle-adapter
```

### 2. **Deep Clean Bluetooth Stack** (Recommended)
This clears Bluetooth cache and resets the stack:

```bash
# As Administrator:
python bluetooth_repair.py "Hesh Evo" --deep-clean --restart-service --toggle-adapter
```

### 3. **Manual Windows Bluetooth Reset**
If the above doesn't work, try these manual steps:

#### Option A: Windows Settings Reset
1. Open **Settings** → **Bluetooth & devices**
2. Click **More Bluetooth settings**
3. In **Bluetooth Settings**, click **Options** tab
4. Check **Allow Bluetooth devices to find this PC**
5. Restart Bluetooth service:
   ```bash
   # As Administrator:
   net stop bthserv
   net start bthserv
   ```

#### Option B: Device Manager Reset
1. Open **Device Manager**
2. Expand **Bluetooth**
3. Right-click your Bluetooth adapter → **Disable device**
4. Wait 10 seconds
5. Right-click → **Enable device**
6. Restart computer

### 4. **Registry-Based Repair** (Advanced)
If WinRT APIs still don't work, try direct registry manipulation:

```bash
# This bypasses WinRT and works directly with registry:
python bluetooth_repair.py --list  # Should show your devices
# Then manually remove and re-add the device
```

### 5. **Windows Bluetooth Troubleshooter**
Run the built-in Windows troubleshooter:
1. **Settings** → **System** → **Troubleshoot**
2. **Other troubleshooters**
3. Run **Bluetooth** troubleshooter

### 6. **Driver Update/Reinstall**
If nothing else works:
1. **Device Manager** → **Bluetooth**
2. Right-click adapter → **Update driver**
3. If that fails: **Uninstall device** → Restart → Let Windows reinstall

## Enhanced Commands Available

### New Troubleshooting Command
```bash
python bluetooth_repair.py --troubleshoot
```
This runs comprehensive diagnostics and tells you exactly what's wrong.

### Deep Clean Command
```bash
python bluetooth_repair.py --deep-clean
```
This performs advanced cleanup:
- Stops Bluetooth service
- Clears all Bluetooth cache directories
- Resets Winsock stack
- Restarts services

### Combined Repair Command
```bash
python bluetooth_repair.py "Hesh Evo" --deep-clean --restart-service --toggle-adapter --timeout 90
```

## Why This Happens
The WinRT Bluetooth APIs in Windows can become "stuck" due to:
- Bluetooth service corruption
- Cached device information conflicts
- Windows Update changes
- Driver issues
- Insufficient privileges

## Expected Results After Fix
Once the WinRT API issue is resolved, you should see:
```
[discover] Scanning available devices...
[discover] Found device: Hesh Evo
[pair] Attempting to pair with device...
[final] Success: 'Hesh Evo' is paired.
```

## Next Steps
1. **Try running as Administrator first** - this solves 80% of WinRT API issues
2. **Use the deep-clean option** if admin alone doesn't work
3. **Check the troubleshooting output** to see if WinRT API starts working
4. **Contact me if issues persist** - we can add more advanced repair options

## Prevention
To prevent this issue in the future:
- Regularly restart the Bluetooth service
- Don't force-quit Bluetooth-related processes
- Keep Windows and Bluetooth drivers updated
- Run Bluetooth operations as Administrator when possible

The enhanced application now provides much better diagnostics and repair capabilities specifically for this type of Windows Bluetooth stack issue!
