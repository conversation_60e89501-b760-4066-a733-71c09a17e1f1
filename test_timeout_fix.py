#!/usr/bin/env python3
"""
Test script to verify timeout handling fixes in bluetooth_repair.py
"""

import subprocess
import sys
import json
import os

def test_timeout_handling():
    """Test that timeout scenarios are handled gracefully."""
    print("Testing timeout handling...")
    
    # Test with a very short timeout to force a timeout scenario
    cmd = "python bluetooth_repair.py \"NonExistentTestDevice12345\" --timeout 3"
    
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            capture_output=True, 
            text=True, 
            timeout=15,  # Give enough time for the test to complete
            cwd=os.path.dirname(os.path.abspath(__file__))
        )
        
        stdout = result.stdout
        stderr = result.stderr
        returncode = result.returncode
        
        print(f"Return code: {returncode}")
        print(f"STDOUT: {stdout}")
        if stderr:
            print(f"STDERR: {stderr}")
        
        # Check that we get a proper error message, not "Unexpected output from helper"
        if returncode != 0:
            if "Device not found" in stdout or "timed out" in stdout.lower():
                print("✓ Timeout handling works correctly")
                return True
            elif "Unexpected output from helper" in stdout:
                print("✗ Still getting 'Unexpected output from helper' error")
                return False
            else:
                print(f"✓ Got a different but reasonable error: {stdout.strip()}")
                return True
        else:
            print("✗ Expected failure but got success")
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ Test itself timed out")
        return False
    except Exception as e:
        print(f"✗ Test failed with exception: {e}")
        return False

def main():
    """Run timeout handling test."""
    print("Testing Bluetooth Repair Timeout Handling")
    print("=" * 50)
    
    if test_timeout_handling():
        print("\n🎉 Timeout handling test passed!")
        return 0
    else:
        print("\n❌ Timeout handling test failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
